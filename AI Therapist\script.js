// Mai Voice Agent - Frontend JavaScript
class MaiVoiceAgent {
    constructor() {
        this.currentMode = 'text';
        this.websocket = null;
        this.webrtcConnection = null;
        this.isConnected = false;
        this.conversationMemory = [];
        this.contactInfo = {};
        
        this.initializeEventListeners();
        this.initializeTextChat();
    }

    initializeEventListeners() {
        // Mode selector buttons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchMode(e.target.dataset.mode);
            });
        });

        // Text chat
        const sendBtn = document.getElementById('sendBtn');
        const chatInput = document.getElementById('chatInput');
        
        sendBtn?.addEventListener('click', () => this.sendTextMessage());
        chatInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendTextMessage();
            }
        });

        // Voice chat
        document.getElementById('startVoiceBtn')?.addEventListener('click', () => this.startVoiceChat());
        document.getElementById('stopVoiceBtn')?.addEventListener('click', () => this.stopVoiceChat());

        // Video chat
        document.getElementById('startVideoBtn')?.addEventListener('click', () => this.startVideoChat());
        document.getElementById('stopVideoBtn')?.addEventListener('click', () => this.stopVideoChat());

        // Contact form
        document.getElementById('contactFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitContactForm();
        });
    }

    switchMode(mode) {
        // Update active button
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

        // Hide all interfaces
        document.querySelectorAll('.chat-interface, .voice-interface, .video-interface, .contact-form').forEach(el => {
            el.style.display = 'none';
            el.classList.remove('active');
        });

        // Show selected interface
        const interfaces = {
            'text': 'textChatContainer',
            'voice': 'voiceChatContainer', 
            'video': 'videoChatContainer',
            'contact': 'contactChatContainer'
        };

        const targetId = interfaces[mode];
        const targetEl = document.getElementById(targetId);
        if (targetEl) {
            targetEl.style.display = mode === 'text' ? 'flex' : 'block';
            targetEl.classList.add('active');
        }

        this.currentMode = mode;

        // Initialize mode-specific functionality
        if (mode === 'text') {
            this.initializeTextChat();
        }
    }

    initializeTextChat() {
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('WebSocket connected');
            this.isConnected = true;
        };

        this.websocket.onmessage = (event) => {
            this.displayMessage(event.data, 'mai');
        };

        this.websocket.onclose = () => {
            console.log('WebSocket disconnected');
            this.isConnected = false;
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.displayMessage('Connection error. Please refresh the page.', 'error');
        };
    }

    sendTextMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();
        
        if (!message || !this.isConnected) return;

        // Display user message
        this.displayMessage(message, 'user');
        
        // Extract contact info from message
        this.extractContactInfo(message);
        
        // Send to server
        this.websocket.send(message);
        
        // Clear input
        input.value = '';
        input.style.height = 'auto';
    }

    displayMessage(content, sender) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = content;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Store in conversation memory
        this.conversationMemory.push({
            content,
            sender,
            timestamp: new Date()
        });
    }

    extractContactInfo(message) {
        const text = message.toLowerCase();
        
        // Extract email
        const emailMatch = message.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
        if (emailMatch && !this.contactInfo.email) {
            this.contactInfo.email = emailMatch[0];
        }

        // Extract phone
        const phoneMatch = message.match(/(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/);
        if (phoneMatch && !this.contactInfo.phone) {
            this.contactInfo.phone = phoneMatch[0];
        }

        // Extract name
        if (text.includes('my name is') && !this.contactInfo.name) {
            const nameStart = text.indexOf('my name is') + 11;
            const nameEnd = text.indexOf('.', nameStart);
            const name = message.substring(nameStart, nameEnd > 0 ? nameEnd : message.length).trim();
            if (name.split(' ').length <= 3) {
                this.contactInfo.name = name;
            }
        }

        // Extract company
        const companyIndicators = ['i work at', "i'm from", 'company is'];
        for (const indicator of companyIndicators) {
            if (text.includes(indicator) && !this.contactInfo.company) {
                const companyStart = text.indexOf(indicator) + indicator.length;
                const companyEnd = text.indexOf('.', companyStart);
                const company = message.substring(companyStart, companyEnd > 0 ? companyEnd : message.length).trim();
                if (company.split(' ').length <= 5) {
                    this.contactInfo.company = company;
                }
                break;
            }
        }
    }

    async startVoiceChat() {
        try {
            const startBtn = document.getElementById('startVoiceBtn');
            const stopBtn = document.getElementById('stopVoiceBtn');
            const status = document.getElementById('voiceStatus');
            
            startBtn.disabled = true;
            status.className = 'status-indicator connecting';
            status.querySelector('span:last-child').textContent = 'Connecting...';

            // Check WebRTC availability
            const response = await fetch('/webrtc/status');
            const statusData = await response.json();
            
            if (!statusData.available) {
                throw new Error('Voice chat not available: ' + statusData.error);
            }

            // Initialize WebRTC connection
            await this.initializeWebRTC('audio');
            
            stopBtn.disabled = false;
            status.className = 'status-indicator connected';
            status.querySelector('span:last-child').textContent = 'Voice chat active';
            
            // Show wave animation
            document.getElementById('waveAnimation').classList.add('active');
            
        } catch (error) {
            console.error('Voice chat error:', error);
            this.displayVoiceMessage('Voice chat unavailable. Please use text chat.', 'error');
            
            const startBtn = document.getElementById('startVoiceBtn');
            const status = document.getElementById('voiceStatus');
            startBtn.disabled = false;
            status.className = 'status-indicator disconnected';
            status.querySelector('span:last-child').textContent = 'Voice chat ready';
        }
    }

    stopVoiceChat() {
        if (this.webrtcConnection) {
            this.webrtcConnection.close();
            this.webrtcConnection = null;
        }

        const startBtn = document.getElementById('startVoiceBtn');
        const stopBtn = document.getElementById('stopVoiceBtn');
        const status = document.getElementById('voiceStatus');
        
        startBtn.disabled = false;
        stopBtn.disabled = true;
        status.className = 'status-indicator disconnected';
        status.querySelector('span:last-child').textContent = 'Voice chat ready';
        
        // Hide wave animation
        document.getElementById('waveAnimation').classList.remove('active');
    }

    async startVideoChat() {
        try {
            const startBtn = document.getElementById('startVideoBtn');
            const stopBtn = document.getElementById('stopVideoBtn');
            const status = document.getElementById('videoStatus');
            
            startBtn.disabled = true;
            status.className = 'status-indicator connecting';
            status.querySelector('span:last-child').textContent = 'Connecting...';

            // Get user media
            const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
            document.getElementById('localVideo').srcObject = stream;

            // Initialize WebRTC connection
            await this.initializeWebRTC('video');
            
            stopBtn.disabled = false;
            status.className = 'status-indicator connected';
            status.querySelector('span:last-child').textContent = 'Video chat active';
            
        } catch (error) {
            console.error('Video chat error:', error);
            this.displayVideoMessage('Video chat unavailable. Please use text chat.', 'error');
            
            const startBtn = document.getElementById('startVideoBtn');
            const status = document.getElementById('videoStatus');
            startBtn.disabled = false;
            status.className = 'status-indicator disconnected';
            status.querySelector('span:last-child').textContent = 'Video chat ready';
        }
    }

    stopVideoChat() {
        // Stop local video stream
        const localVideo = document.getElementById('localVideo');
        if (localVideo.srcObject) {
            localVideo.srcObject.getTracks().forEach(track => track.stop());
            localVideo.srcObject = null;
        }

        if (this.webrtcConnection) {
            this.webrtcConnection.close();
            this.webrtcConnection = null;
        }

        const startBtn = document.getElementById('startVideoBtn');
        const stopBtn = document.getElementById('stopVideoBtn');
        const status = document.getElementById('videoStatus');
        
        startBtn.disabled = false;
        stopBtn.disabled = true;
        status.className = 'status-indicator disconnected';
        status.querySelector('span:last-child').textContent = 'Video chat ready';
    }

    async initializeWebRTC(mode) {
        // This would integrate with the FastRTC backend
        // For now, show a placeholder message
        const message = `${mode} chat would connect to Mai's voice system here.`;
        if (mode === 'audio') {
            this.displayVoiceMessage(message, 'system');
        } else {
            this.displayVideoMessage(message, 'system');
        }
    }

    displayVoiceMessage(content, sender) {
        const container = document.getElementById('voiceMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `voice-message ${sender}`;
        messageDiv.textContent = content;
        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    displayVideoMessage(content, sender) {
        const container = document.getElementById('videoMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `video-message ${sender}`;
        messageDiv.textContent = content;
        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
    }

    async submitContactForm() {
        const form = document.getElementById('contactFormElement');
        const formData = new FormData(form);
        
        const contactData = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            company: formData.get('company'),
            purpose: formData.get('purpose')
        };

        try {
            const response = await fetch('/api/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData)
            });

            const result = await response.json();
            
            if (response.ok) {
                // Show success message
                form.innerHTML = `
                    <div class="success-message">
                        <h3>✅ Thank you for contacting Critical Future!</h3>
                        <p>We've received your inquiry and will be in touch shortly.</p>
                        <p>A confirmation email has been sent to ${contactData.email}</p>
                    </div>
                `;
            } else {
                throw new Error(result.message || 'Failed to submit form');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            alert('There was an error submitting your form. Please try again.');
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.maiAgent = new MaiVoiceAgent();
});
