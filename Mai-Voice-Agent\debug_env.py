#!/usr/bin/env python3
"""
Debug .env file loading
"""

import os

# Read .env file manually
env_path = '.env'
print(f"Reading {env_path}...")

with open(env_path, 'r', encoding='utf-8') as f:
    lines = f.readlines()

print(f"File has {len(lines)} lines")

# Look for email settings
for i, line in enumerate(lines, 1):
    line = line.strip()
    if 'EMAIL_ADDRESS' in line or 'EMAIL_PASSWORD' in line:
        print(f"Line {i}: {repr(line)}")

# Try loading with dotenv
from dotenv import load_dotenv
load_dotenv(env_path)

print("\nAfter load_dotenv:")
print(f"EMAIL_ADDRESS: {repr(os.getenv('EMAIL_ADDRESS'))}")
print(f"EMAIL_PASSWORD: {repr(os.getenv('EMAIL_PASSWORD'))}")

# Try loading with explicit override
load_dotenv(env_path, override=True)

print("\nAfter load_dotenv with override:")
print(f"EMAIL_ADDRESS: {repr(os.getenv('EMAIL_ADDRESS'))}")
print(f"EMAIL_PASSWORD: {repr(os.getenv('EMAIL_PASSWORD'))}")

# Try setting manually
os.environ['EMAIL_ADDRESS'] = '<EMAIL>'
os.environ['EMAIL_PASSWORD'] = 'lceg dmyy fvwm fkor'

print("\nAfter manual setting:")
print(f"EMAIL_ADDRESS: {repr(os.getenv('EMAIL_ADDRESS'))}")
print(f"EMAIL_PASSWORD: {repr(os.getenv('EMAIL_PASSWORD'))}")

# Test pydantic with manual settings
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
    
    class TestSettings(BaseSettings):
        gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
        email_address: str = Field(..., env="EMAIL_ADDRESS")
        email_password: str = Field(..., env="EMAIL_PASSWORD")
        
        class Config:
            env_file = ".env"
    
    settings = TestSettings()
    print("\n✅ Pydantic settings loaded successfully!")
    
except Exception as e:
    print(f"\n❌ Pydantic settings still failed: {e}")
