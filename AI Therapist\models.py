"""
Data models for Mai Voice Agent
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, EmailStr


class SessionType(Enum):
    """Types of conversation sessions"""
    TEXT = "text"
    VOICE = "voice"
    VIDEO = "video"
    CONTACT_FORM = "contact_form"


class MessageType(Enum):
    """Types of messages in a conversation"""
    USER = "user"
    MAI = "mai"
    SYSTEM = "system"


class ContactInfo(BaseModel):
    """Contact information captured during conversation"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    company: Optional[str] = None
    purpose: Optional[str] = None
    
    def is_complete(self) -> bool:
        """Check if we have minimum required contact info"""
        return bool(self.name and self.email)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for email templates"""
        return {
            "name": self.name or "Valued Contact",
            "email": self.email or "Not provided",
            "phone": self.phone or "Not provided", 
            "company": self.company or "Not specified",
            "purpose": self.purpose or "General enquiry"
        }


class ConversationMessage(BaseModel):
    """A single message in a conversation"""
    type: MessageType
    content: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Optional[Dict[str, Any]] = None


class ConversationMemory(BaseModel):
    """Complete conversation memory for a session"""
    session_id: str
    session_type: SessionType
    started_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    messages: List[ConversationMessage] = Field(default_factory=list)
    contact_info: ContactInfo = Field(default_factory=ContactInfo)
    
    def add_message(self, message_type: MessageType, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a message to the conversation"""
        message = ConversationMessage(
            type=message_type,
            content=content,
            metadata=metadata
        )
        self.messages.append(message)
        self.last_activity = datetime.utcnow()
    
    def extract_contact_info(self, text: str):
        """Extract contact information from conversation text"""
        # Simple extraction logic - can be enhanced with NLP
        text_lower = text.lower()
        
        # Extract email patterns
        import re
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails and not self.contact_info.email:
            self.contact_info.email = emails[0]
        
        # Extract phone patterns
        phone_pattern = r'(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        phones = re.findall(phone_pattern, text)
        if phones and not self.contact_info.phone:
            self.contact_info.phone = ''.join(phones[0])
        
        # Extract name patterns (simple heuristics)
        if "my name is" in text_lower and not self.contact_info.name:
            name_start = text_lower.find("my name is") + 11
            name_end = text_lower.find(".", name_start)
            if name_end == -1:
                name_end = text_lower.find(",", name_start)
            if name_end == -1:
                name_end = len(text)
            potential_name = text[name_start:name_end].strip()
            if len(potential_name.split()) <= 3:  # Reasonable name length
                self.contact_info.name = potential_name
        
        # Extract company patterns
        company_indicators = ["i work at", "i'm from", "company is", "organization is"]
        for indicator in company_indicators:
            if indicator in text_lower and not self.contact_info.company:
                company_start = text_lower.find(indicator) + len(indicator)
                company_end = text_lower.find(".", company_start)
                if company_end == -1:
                    company_end = text_lower.find(",", company_start)
                if company_end == -1:
                    company_end = len(text)
                potential_company = text[company_start:company_end].strip()
                if len(potential_company.split()) <= 5:  # Reasonable company name length
                    self.contact_info.company = potential_company
                break


class EmailResponse(BaseModel):
    """Response from email sending operations"""
    status: str  # "success", "error", "partial"
    message: str
    emails_sent: int = 0


class ContactFormSubmission(BaseModel):
    """Contact form submission data"""
    name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    phone: Optional[str] = Field(None, max_length=20)
    company: Optional[str] = Field(None, max_length=100)
    purpose: str = Field(..., min_length=1, max_length=1000)
    
    def to_contact_info(self) -> ContactInfo:
        """Convert form submission to ContactInfo"""
        return ContactInfo(
            name=self.name,
            email=str(self.email),
            phone=self.phone,
            company=self.company,
            purpose=self.purpose
        )


class VoiceSettings(BaseModel):
    """Voice configuration settings"""
    voice_name: str = "Aoede"
    temperature: float = 0.8
    max_tokens: int = 256
    
    @classmethod
    def get_available_voices(cls) -> List[Dict[str, str]]:
        """Get list of available Gemini voices"""
        return [
            {"id": "Puck", "name": "Puck", "description": "Warm, conversational voice"},
            {"id": "Charon", "name": "Charon", "description": "Calm, reassuring voice"},
            {"id": "Kore", "name": "Kore", "description": "Gentle, empathetic voice"},
            {"id": "Fenrir", "name": "Fenrir", "description": "Strong, supportive voice"},
            {"id": "Aoede", "name": "Aoede", "description": "Melodic, soothing voice (recommended)"},
        ]


class SessionSummary(BaseModel):
    """Summary of a completed session"""
    session_id: str
    session_type: SessionType
    duration_seconds: int
    message_count: int
    contact_captured: bool
    contact_info: Optional[ContactInfo] = None
    key_topics: List[str] = Field(default_factory=list)
    follow_up_sent: bool = False
