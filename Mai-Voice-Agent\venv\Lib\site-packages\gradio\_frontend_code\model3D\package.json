{"name": "@gradio/model3d", "version": "0.14.15", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@gradio/atoms": "workspace:^", "@gradio/client": "workspace:^", "@gradio/icons": "workspace:^", "@gradio/statustracker": "workspace:^", "@gradio/upload": "workspace:^", "@gradio/utils": "workspace:^", "@gradio/wasm": "workspace:^", "@types/babylon": "^6.16.6", "@babylonjs/viewer": "^8.2.0", "@babylonjs/core": "^8.2.0", "@babylonjs/loaders": "^8.2.0", "dequal": "^2.0.2", "gsplat": "^1.0.5"}, "devDependencies": {"@gradio/preview": "workspace:^"}, "main_changeset": true, "main": "./Index.svelte", "exports": {".": {"gradio": "./Index.svelte", "svelte": "./dist/Index.svelte", "types": "./dist/Index.svelte.d.ts"}, "./example": {"gradio": "./Example.svelte", "svelte": "./dist/Example.svelte", "types": "./dist/Example.svelte.d.ts"}, "./package.json": "./package.json"}, "peerDependencies": {"svelte": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/model3D"}}