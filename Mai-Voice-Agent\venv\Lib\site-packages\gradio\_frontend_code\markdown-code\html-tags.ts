// https://www.w3schools.com/tags/
export const standardHtmlTags = [
	"!--",
	"!doctype",
	"a",
	"abbr",
	"acronym",
	"address",
	"applet",
	"area",
	"article",
	"aside",
	"audio",
	"b",
	"base",
	"basefont",
	"bdi",
	"bdo",
	"big",
	"blockquote",
	"body",
	"br",
	"button",
	"canvas",
	"caption",
	"center",
	"cite",
	"code",
	"col",
	"colgroup",
	"data",
	"datalist",
	"dd",
	"del",
	"details",
	"dfn",
	"dialog",
	"dir",
	"div",
	"dl",
	"dt",
	"em",
	"embed",
	"fieldset",
	"figcaption",
	"figure",
	"font",
	"footer",
	"form",
	"frame",
	"frameset",
	"h1",
	"h2",
	"h3",
	"h4",
	"h5",
	"h6",
	"head",
	"header",
	"hgroup",
	"hr",
	"html",
	"i",
	"iframe",
	"img",
	"input",
	"ins",
	"kbd",
	"label",
	"legend",
	"li",
	"link",
	"main",
	"map",
	"mark",
	"menu",
	"meta",
	"meter",
	"nav",
	"noframes",
	"noscript",
	"object",
	"ol",
	"optgroup",
	"option",
	"output",
	"p",
	"param",
	"picture",
	"pre",
	"progress",
	"q",
	"rp",
	"rt",
	"ruby",
	"s",
	"samp",
	"script",
	"search",
	"section",
	"select",
	"small",
	"source",
	"span",
	"strike",
	"strong",
	"style",
	"sub",
	"summary",
	"sup",
	"svg",
	"table",
	"tbody",
	"td",
	"template",
	"textarea",
	"tfoot",
	"th",
	"thead",
	"time",
	"title",
	"tr",
	"track",
	"tt",
	"u",
	"ul",
	"var",
	"video",
	"wbr"
];

// SVG tags
export const svgTags = [
	// Base structural elements
	"g",
	"defs",
	"use",
	"symbol",

	// Shape elements
	"rect",
	"circle",
	"ellipse",
	"line",
	"polyline",
	"polygon",
	"path",
	"image",

	// Text elements
	"text",
	"tspan",
	"textPath",

	// Gradient and effects
	"linearGradient",
	"radialGradient",
	"stop",
	"pattern",
	"clipPath",
	"mask",
	"filter",

	// Filter effects
	"feBlend",
	"feColorMatrix",
	"feComponentTransfer",
	"feComposite",
	"feConvolveMatrix",
	"feDiffuseLighting",
	"feDisplacementMap",
	"feGaussianBlur",
	"feMerge",
	"feMorphology",
	"feOffset",
	"feSpecularLighting",
	"feTurbulence",
	"feMergeNode",
	"feFuncR",
	"feFuncG",
	"feFuncB",
	"feFuncA",
	"feDistantLight",
	"fePointLight",
	"feSpotLight",
	"feFlood",
	"feTile",

	// Animation elements
	"animate",
	"animateTransform",
	"animateMotion",
	"mpath",
	"set",

	// Interactive and other elements
	"view",
	"cursor",
	"foreignObject",
	"desc",
	"title",
	"metadata",
	"switch"
];

export const standardHtmlAndSvgTags = [
	...standardHtmlTags,
	...svgTags.filter((tag) => !standardHtmlTags.includes(tag))
];
