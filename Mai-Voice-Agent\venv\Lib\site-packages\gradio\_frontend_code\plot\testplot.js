export const matplotlib_plot = {
	type: "matplotlib",
	plot: "data:image/png;base64,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"
};

export let bar_plot = {
	type: "altair",
	plot: '{\n "$schema": "https://vega.github.io/schema/vega-lite/v5.17.0.json",\n "background": "transparent",\n "config": {\n "view": {\n "continuousHeight": 300,\n "continuousWidth": 300\n }\n },\n "data": {\n "name": "data-7684334a68ab74ba4c3ca7d0ceaeb461"\n },\n "datasets": {\n "data-7684334a68ab74ba4c3ca7d0ceaeb461": [\n {\n "a": "A",\n "b": 28\n },\n {\n "a": "B",\n "b": 55\n },\n {\n "a": "C",\n "b": 43\n },\n {\n "a": "D",\n "b": 91\n },\n {\n "a": "E",\n "b": 81\n },\n {\n "a": "F",\n "b": 53\n },\n {\n "a": "G",\n "b": 19\n },\n {\n "a": "H",\n "b": 87\n },\n {\n "a": "I",\n "b": 52\n }\n ]\n },\n "encoding": {\n "tooltip": [\n {\n "field": "a",\n "type": "nominal"\n },\n {\n "field": "b",\n "type": "quantitative"\n }\n ],\n "x": {\n "axis": {},\n "field": "a",\n "sort": null,\n "title": "a",\n "type": "nominal"\n },\n "y": {\n "aggregate": "sum",\n "axis": {},\n "field": "b",\n "scale": {\n "domain": [\n 20,\n 100\n ]\n },\n "sort": null,\n "title": "b",\n "type": "quantitative"\n }\n },\n "mark": {\n "type": "bar"\n },\n "params": [\n {\n "bind": "scales",\n "name": "param_9",\n "select": {\n "encodings": [\n "x",\n "y"\n ],\n "type": "interval"\n }\n }\n ],\n "title": "Simple Bar Plot with made up data"\n}',
	chart: "bar"
};

export let line_plot = {
	type: "altair",
	plot: '{\n "$schema": "https://vega.github.io/schema/vega-lite/v5.17.0.json",\n "background": "transparent",\n "config": {\n "view": {\n "continuousHeight": 300,\n "continuousWidth": 300\n }\n },\n "data": {\n "name": "data-2cfbf5a7da997fa4bec9dfb6b7046a44"\n },\n "datasets": {\n "data-2cfbf5a7da997fa4bec9dfb6b7046a44": [\n {\n "DATE": "2010-01-01T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 344,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 339,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10213,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 282,\n "HLY-PRES-NORMAL": 10217,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10221,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 293,\n "HLY-PRES-NORMAL": 10220,\n "HLY-TEMP-NORMAL": 394,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 421,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 291,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 442,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 456,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 462,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 458,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 412,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 382,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 372,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 363,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-01T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 355,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 344,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 339,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 335,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10213,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10217,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 291,\n "HLY-PRES-NORMAL": 10220,\n "HLY-TEMP-NORMAL": 366,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 295,\n "HLY-PRES-NORMAL": 10219,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 295,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 424,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 294,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 445,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 291,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 460,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 465,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 461,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 444,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 415,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 291,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 398,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 385,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 374,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 365,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-02T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 291,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 358,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 351,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 337,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 282,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 333,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 329,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10220,\n "HLY-TEMP-NORMAL": 367,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 296,\n "HLY-PRES-NORMAL": 10219,\n "HLY-TEMP-NORMAL": 397,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 297,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 425,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 295,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 445,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 459,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 465,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 460,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 443,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 415,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 398,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 293,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 385,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 293,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 374,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 293,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 365,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-03T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 357,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 350,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 345,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 336,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 324,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10215,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10219,\n "HLY-TEMP-NORMAL": 366,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 294,\n "HLY-PRES-NORMAL": 10218,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 294,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 423,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 292,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 443,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 456,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 462,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 457,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 413,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 395,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 383,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 372,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-04T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 356,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 348,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 337,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 332,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 324,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10215,\n "HLY-TEMP-NORMAL": 335,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10219,\n "HLY-TEMP-NORMAL": 363,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10218,\n "HLY-TEMP-NORMAL": 393,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 291,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 421,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 455,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 460,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 456,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 439,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 411,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 394,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 382,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 371,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 288,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 363,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-05T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 355,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 348,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 282,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 337,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 332,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 335,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10219,\n "HLY-TEMP-NORMAL": 363,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10219,\n "HLY-TEMP-NORMAL": 393,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 289,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 420,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 440,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 453,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 459,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 455,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 282,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 411,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 394,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 381,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 371,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 362,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-06T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 354,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 347,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 282,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 336,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 332,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10217,\n "HLY-TEMP-NORMAL": 362,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 392,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 290,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 419,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 287,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 440,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 453,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10187,\n "HLY-TEMP-NORMAL": 459,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 454,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 281,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 410,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 393,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 380,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 370,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 286,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 361,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-07T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 353,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 282,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 330,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 326,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 271,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10213,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 283,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 359,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 285,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 390,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 284,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 416,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 437,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 277,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 451,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10187,\n "HLY-TEMP-NORMAL": 457,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 452,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 436,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 408,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 390,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 377,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 367,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 357,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-08T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 343,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 336,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 271,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 326,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 319,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 316,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 313,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 278,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 356,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 280,\n "HLY-PRES-NORMAL": 10216,\n "HLY-TEMP-NORMAL": 386,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 279,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 413,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 434,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 448,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 454,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 450,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 433,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 271,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 405,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 387,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 375,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 354,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-09T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 333,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 313,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 308,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10215,\n "HLY-TEMP-NORMAL": 354,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 276,\n "HLY-PRES-NORMAL": 10215,\n "HLY-TEMP-NORMAL": 384,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 275,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 411,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 431,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 446,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 452,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10187,\n "HLY-TEMP-NORMAL": 448,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 432,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 403,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 386,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 373,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 362,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-10T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 344,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 326,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 313,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 309,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 307,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 272,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 274,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 382,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 273,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 409,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 430,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10187,\n "HLY-TEMP-NORMAL": 444,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 451,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 447,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 432,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 403,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 385,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 270,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 372,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 271,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 361,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 271,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 351,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-11T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 343,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 337,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 330,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 326,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 313,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 308,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10213,\n "HLY-TEMP-NORMAL": 350,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 269,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 379,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 406,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 426,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 448,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 444,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 429,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 400,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 382,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 369,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 358,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-12T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 319,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 308,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 348,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 377,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 403,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 424,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 444,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 426,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 397,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 379,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 367,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 355,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-13T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 316,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 308,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 303,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 345,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 374,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 401,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 421,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 435,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 423,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 394,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 376,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-14T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 335,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 312,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 307,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 302,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 299,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 297,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 371,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 398,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 419,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10187,\n "HLY-TEMP-NORMAL": 434,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 437,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 422,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 394,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 375,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 363,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 351,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-15T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 333,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 302,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 300,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 297,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 295,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 312,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10214,\n "HLY-TEMP-NORMAL": 370,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 398,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 419,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 435,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 442,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10189,\n "HLY-TEMP-NORMAL": 424,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 376,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-16T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 316,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 303,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 298,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 296,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 371,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 399,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 420,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 435,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 424,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 395,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 376,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-17T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 316,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 302,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 300,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 297,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 295,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 371,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 399,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 421,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 436,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10181,\n "HLY-TEMP-NORMAL": 442,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 439,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 425,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 377,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-18T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 334,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 243,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 299,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 296,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 294,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 313,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 370,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 398,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 420,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 436,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10181,\n "HLY-TEMP-NORMAL": 442,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 439,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 425,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 397,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 377,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 351,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-19T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 333,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 326,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 309,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 300,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 297,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 294,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 240,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 293,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 339,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 368,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 418,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 434,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 243,\n "HLY-PRES-NORMAL": 10181,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 243,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 243,\n "HLY-PRES-NORMAL": 10187,\n "HLY-TEMP-NORMAL": 425,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 396,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 375,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 362,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 339,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-20T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 324,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 297,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 239,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 294,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 237,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 292,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 237,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 290,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10212,\n "HLY-TEMP-NORMAL": 368,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 397,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 419,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 435,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 442,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 439,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 426,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 397,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 376,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 362,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 339,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-21T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 324,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 312,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 307,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 298,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 239,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 295,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 238,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 292,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 238,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 291,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10213,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10213,\n "HLY-TEMP-NORMAL": 370,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 399,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 420,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 437,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 443,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 440,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10188,\n "HLY-TEMP-NORMAL": 428,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 399,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 378,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 351,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-22T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 333,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 319,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 309,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 243,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 298,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 241,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 295,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 242,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 294,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 342,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10211,\n "HLY-TEMP-NORMAL": 373,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 402,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 422,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 439,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 445,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 442,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10186,\n "HLY-TEMP-NORMAL": 429,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 401,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 380,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 367,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10201,\n "HLY-TEMP-NORMAL": 354,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 344,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-23T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 336,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 316,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 312,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 303,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 300,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 244,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 298,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 243,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 297,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 316,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 344,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10210,\n "HLY-TEMP-NORMAL": 375,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 403,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 424,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 447,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 445,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10185,\n "HLY-TEMP-NORMAL": 432,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 404,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 383,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 369,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 356,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-24T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 330,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 324,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 309,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 246,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 302,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 300,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 245,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 298,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 377,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 406,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 427,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 444,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 450,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 448,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 435,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 407,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 386,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 372,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 358,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 348,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-25T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 340,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 255,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 319,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 307,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 303,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 247,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 300,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10206,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 381,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 410,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 431,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10182,\n "HLY-TEMP-NORMAL": 448,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 454,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 451,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 410,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 389,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 374,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 360,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 349,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-26T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 341,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 332,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 319,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 314,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 309,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 250,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 302,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 248,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 249,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 301,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 322,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 352,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 384,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 414,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 436,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 453,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 459,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 457,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 443,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 416,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 393,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 378,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 364,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 353,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-27T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 344,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 335,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 328,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 323,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 318,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 313,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 310,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10204,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10207,\n "HLY-TEMP-NORMAL": 326,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 356,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 388,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 417,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 438,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 455,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 461,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 459,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 446,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 418,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10196,\n "HLY-TEMP-NORMAL": 395,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 379,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 366,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 355,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-28T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 337,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 330,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10193,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 307,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 304,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 357,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 390,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10203,\n "HLY-TEMP-NORMAL": 418,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 439,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 456,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 462,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 460,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 447,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 419,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 397,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 381,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 367,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 356,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-29T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 346,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 338,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 331,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 262,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 325,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 320,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 315,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 256,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 311,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 307,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 251,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 305,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 358,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 391,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 419,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 440,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 457,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 463,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 461,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 448,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 421,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 398,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 382,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 369,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 357,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-30T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 266,\n "HLY-PRES-NORMAL": 10198,\n "HLY-TEMP-NORMAL": 348,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T00:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10197,\n "HLY-TEMP-NORMAL": 339,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T01:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 332,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T02:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 327,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T03:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 321,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T04:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 317,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T05:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10191,\n "HLY-TEMP-NORMAL": 312,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T06:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 254,\n "HLY-PRES-NORMAL": 10194,\n "HLY-TEMP-NORMAL": 308,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T07:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 252,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T08:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 253,\n "HLY-PRES-NORMAL": 10205,\n "HLY-TEMP-NORMAL": 306,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T09:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 329,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T10:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 268,\n "HLY-PRES-NORMAL": 10209,\n "HLY-TEMP-NORMAL": 360,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T11:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 267,\n "HLY-PRES-NORMAL": 10208,\n "HLY-TEMP-NORMAL": 393,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T12:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10202,\n "HLY-TEMP-NORMAL": 421,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T13:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 260,\n "HLY-PRES-NORMAL": 10192,\n "HLY-TEMP-NORMAL": 441,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T14:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10183,\n "HLY-TEMP-NORMAL": 458,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T15:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 257,\n "HLY-PRES-NORMAL": 10179,\n "HLY-TEMP-NORMAL": 465,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T16:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 258,\n "HLY-PRES-NORMAL": 10180,\n "HLY-TEMP-NORMAL": 462,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T17:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 259,\n "HLY-PRES-NORMAL": 10184,\n "HLY-TEMP-NORMAL": 449,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T18:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 261,\n "HLY-PRES-NORMAL": 10190,\n "HLY-TEMP-NORMAL": 422,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T19:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 263,\n "HLY-PRES-NORMAL": 10195,\n "HLY-TEMP-NORMAL": 399,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T20:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 264,\n "HLY-PRES-NORMAL": 10199,\n "HLY-TEMP-NORMAL": 382,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T21:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 369,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T22:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 357,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n },\n {\n "DATE": "2010-01-31T23:00:00",\n "ELEVATION": 645.3,\n "HLY-DEWP-NORMAL": 265,\n "HLY-PRES-NORMAL": 10200,\n "HLY-TEMP-NORMAL": 347,\n "LATITUDE": 35.43194,\n "LONGITUDE": -82.5375,\n "STATION": "GHCND:USW00003812",\n "STATION_NAME": "ASHEVILLE REGIONAL AIRPORT NC US"\n }\n ]\n },\n "layer": [\n {\n "encoding": {\n "strokeDash": {},\n "tooltip": [\n {\n "field": "DATE",\n "type": "temporal"\n },\n {\n "field": "HLY-TEMP-NORMAL",\n "type": "quantitative"\n }\n ],\n "x": {\n "axis": {},\n "field": "DATE",\n "title": "DATE",\n "type": "temporal"\n },\n "y": {\n "axis": {},\n "field": "HLY-TEMP-NORMAL",\n "scale": {\n "domain": [\n 250,\n 500\n ]\n },\n "title": "HLY-TEMP-NORMAL",\n "type": "quantitative"\n }\n },\n "mark": {\n "clip": true,\n "type": "line"\n },\n "name": "view_3"\n },\n {\n "encoding": {\n "opacity": {\n "value": 0\n },\n "tooltip": [\n {\n "field": "DATE",\n "type": "temporal"\n },\n {\n "field": "HLY-TEMP-NORMAL",\n "type": "quantitative"\n }\n ],\n "x": {\n "axis": {},\n "field": "DATE",\n "title": "DATE",\n "type": "temporal"\n },\n "y": {\n "axis": {},\n "field": "HLY-TEMP-NORMAL",\n "scale": {\n "domain": [\n 250,\n 500\n ]\n },\n "title": "HLY-TEMP-NORMAL",\n "type": "quantitative"\n }\n },\n "mark": {\n "clip": true,\n "type": "point"\n }\n }\n ],\n "params": [\n {\n "bind": "scales",\n "name": "param_7",\n "select": {\n "encodings": [\n "x",\n "y"\n ],\n "type": "interval"\n },\n "views": [\n "view_3"\n ]\n }\n ],\n "title": "Climate"\n}',
	chart: "line"
};

export let multi_line_plot = {
	type: "altair",
	plot: '{\n "$schema": "https://vega.github.io/schema/vega-lite/v5.17.0.json",\n "background": "transparent",\n "config": {\n "view": {\n "continuousHeight": 300,\n "continuousWidth": 300\n }\n },\n "data": {\n "name": "data-b0fe595546d47c5085f225c35730172c"\n },\n "datasets": {\n "data-b0fe595546d47c5085f225c35730172c": [\n {\n "date": "2000-01-01T00:00:00",\n "price": 39.81,\n "symbol": "MSFT"\n },\n {\n "date": "2000-02-01T00:00:00",\n "price": 36.35,\n "symbol": "MSFT"\n },\n {\n "date": "2000-03-01T00:00:00",\n "price": 43.22,\n "symbol": "MSFT"\n },\n {\n "date": "2000-04-01T00:00:00",\n "price": 28.37,\n "symbol": "MSFT"\n },\n {\n "date": "2000-05-01T00:00:00",\n "price": 25.45,\n "symbol": "MSFT"\n },\n {\n "date": "2000-06-01T00:00:00",\n "price": 32.54,\n "symbol": "MSFT"\n },\n {\n "date": "2000-07-01T00:00:00",\n "price": 28.4,\n "symbol": "MSFT"\n },\n {\n "date": "2000-08-01T00:00:00",\n "price": 28.4,\n "symbol": "MSFT"\n },\n {\n "date": "2000-09-01T00:00:00",\n "price": 24.53,\n "symbol": "MSFT"\n },\n {\n "date": "2000-10-01T00:00:00",\n "price": 28.02,\n "symbol": "MSFT"\n },\n {\n "date": "2000-11-01T00:00:00",\n "price": 23.34,\n "symbol": "MSFT"\n },\n {\n "date": "2000-12-01T00:00:00",\n "price": 17.65,\n "symbol": "MSFT"\n },\n {\n "date": "2001-01-01T00:00:00",\n "price": 24.84,\n "symbol": "MSFT"\n },\n {\n "date": "2001-02-01T00:00:00",\n "price": 24.0,\n "symbol": "MSFT"\n },\n {\n "date": "2001-03-01T00:00:00",\n "price": 22.25,\n "symbol": "MSFT"\n },\n {\n "date": "2001-04-01T00:00:00",\n "price": 27.56,\n "symbol": "MSFT"\n },\n {\n "date": "2001-05-01T00:00:00",\n "price": 28.14,\n "symbol": "MSFT"\n },\n {\n "date": "2001-06-01T00:00:00",\n "price": 29.7,\n "symbol": "MSFT"\n },\n {\n "date": "2001-07-01T00:00:00",\n "price": 26.93,\n "symbol": "MSFT"\n },\n {\n "date": "2001-08-01T00:00:00",\n "price": 23.21,\n "symbol": "MSFT"\n },\n {\n "date": "2001-09-01T00:00:00",\n "price": 20.82,\n "symbol": "MSFT"\n },\n {\n "date": "2001-10-01T00:00:00",\n "price": 23.65,\n "symbol": "MSFT"\n },\n {\n "date": "2001-11-01T00:00:00",\n "price": 26.12,\n "symbol": "MSFT"\n },\n {\n "date": "2001-12-01T00:00:00",\n "price": 26.95,\n "symbol": "MSFT"\n },\n {\n "date": "2002-01-01T00:00:00",\n "price": 25.92,\n "symbol": "MSFT"\n },\n {\n "date": "2002-02-01T00:00:00",\n "price": 23.73,\n "symbol": "MSFT"\n },\n {\n "date": "2002-03-01T00:00:00",\n "price": 24.53,\n "symbol": "MSFT"\n },\n {\n "date": "2002-04-01T00:00:00",\n "price": 21.26,\n "symbol": "MSFT"\n },\n {\n "date": "2002-05-01T00:00:00",\n "price": 20.71,\n "symbol": "MSFT"\n },\n {\n "date": "2002-06-01T00:00:00",\n "price": 22.25,\n "symbol": "MSFT"\n },\n {\n "date": "2002-07-01T00:00:00",\n "price": 19.52,\n "symbol": "MSFT"\n },\n {\n "date": "2002-08-01T00:00:00",\n "price": 19.97,\n "symbol": "MSFT"\n },\n {\n "date": "2002-09-01T00:00:00",\n "price": 17.79,\n "symbol": "MSFT"\n },\n {\n "date": "2002-10-01T00:00:00",\n "price": 21.75,\n "symbol": "MSFT"\n },\n {\n "date": "2002-11-01T00:00:00",\n "price": 23.46,\n "symbol": "MSFT"\n },\n {\n "date": "2002-12-01T00:00:00",\n "price": 21.03,\n "symbol": "MSFT"\n },\n {\n "date": "2003-01-01T00:00:00",\n "price": 19.31,\n "symbol": "MSFT"\n },\n {\n "date": "2003-02-01T00:00:00",\n "price": 19.34,\n "symbol": "MSFT"\n },\n {\n "date": "2003-03-01T00:00:00",\n "price": 19.76,\n "symbol": "MSFT"\n },\n {\n "date": "2003-04-01T00:00:00",\n "price": 20.87,\n "symbol": "MSFT"\n },\n {\n "date": "2003-05-01T00:00:00",\n "price": 20.09,\n "symbol": "MSFT"\n },\n {\n "date": "2003-06-01T00:00:00",\n "price": 20.93,\n "symbol": "MSFT"\n },\n {\n "date": "2003-07-01T00:00:00",\n "price": 21.56,\n "symbol": "MSFT"\n },\n {\n "date": "2003-08-01T00:00:00",\n "price": 21.65,\n "symbol": "MSFT"\n },\n {\n "date": "2003-09-01T00:00:00",\n "price": 22.69,\n "symbol": "MSFT"\n },\n {\n "date": "2003-10-01T00:00:00",\n "price": 21.45,\n "symbol": "MSFT"\n },\n {\n "date": "2003-11-01T00:00:00",\n "price": 21.1,\n "symbol": "MSFT"\n },\n {\n "date": "2003-12-01T00:00:00",\n "price": 22.46,\n "symbol": "MSFT"\n },\n {\n "date": "2004-01-01T00:00:00",\n "price": 22.69,\n "symbol": "MSFT"\n },\n {\n "date": "2004-02-01T00:00:00",\n "price": 21.77,\n "symbol": "MSFT"\n },\n {\n "date": "2004-03-01T00:00:00",\n "price": 20.46,\n "symbol": "MSFT"\n },\n {\n "date": "2004-04-01T00:00:00",\n "price": 21.45,\n "symbol": "MSFT"\n },\n {\n "date": "2004-05-01T00:00:00",\n "price": 21.53,\n "symbol": "MSFT"\n },\n {\n "date": "2004-06-01T00:00:00",\n "price": 23.44,\n "symbol": "MSFT"\n },\n {\n "date": "2004-07-01T00:00:00",\n "price": 23.38,\n "symbol": "MSFT"\n },\n {\n "date": "2004-08-01T00:00:00",\n "price": 22.47,\n "symbol": "MSFT"\n },\n {\n "date": "2004-09-01T00:00:00",\n "price": 22.76,\n "symbol": "MSFT"\n },\n {\n "date": "2004-10-01T00:00:00",\n "price": 23.02,\n "symbol": "MSFT"\n },\n {\n "date": "2004-11-01T00:00:00",\n "price": 24.6,\n "symbol": "MSFT"\n },\n {\n "date": "2004-12-01T00:00:00",\n "price": 24.52,\n "symbol": "MSFT"\n },\n {\n "date": "2005-01-01T00:00:00",\n "price": 24.11,\n "symbol": "MSFT"\n },\n {\n "date": "2005-02-01T00:00:00",\n "price": 23.15,\n "symbol": "MSFT"\n },\n {\n "date": "2005-03-01T00:00:00",\n "price": 22.24,\n "symbol": "MSFT"\n },\n {\n "date": "2005-04-01T00:00:00",\n "price": 23.28,\n "symbol": "MSFT"\n },\n {\n "date": "2005-05-01T00:00:00",\n "price": 23.82,\n "symbol": "MSFT"\n },\n {\n "date": "2005-06-01T00:00:00",\n "price": 22.93,\n "symbol": "MSFT"\n },\n {\n "date": "2005-07-01T00:00:00",\n "price": 23.64,\n "symbol": "MSFT"\n },\n {\n "date": "2005-08-01T00:00:00",\n "price": 25.35,\n "symbol": "MSFT"\n },\n {\n "date": "2005-09-01T00:00:00",\n "price": 23.83,\n "symbol": "MSFT"\n },\n {\n "date": "2005-10-01T00:00:00",\n "price": 23.8,\n "symbol": "MSFT"\n },\n {\n "date": "2005-11-01T00:00:00",\n "price": 25.71,\n "symbol": "MSFT"\n },\n {\n "date": "2005-12-01T00:00:00",\n "price": 24.29,\n "symbol": "MSFT"\n },\n {\n "date": "2006-01-01T00:00:00",\n "price": 26.14,\n "symbol": "MSFT"\n },\n {\n "date": "2006-02-01T00:00:00",\n "price": 25.04,\n "symbol": "MSFT"\n },\n {\n "date": "2006-03-01T00:00:00",\n "price": 25.36,\n "symbol": "MSFT"\n },\n {\n "date": "2006-04-01T00:00:00",\n "price": 22.5,\n "symbol": "MSFT"\n },\n {\n "date": "2006-05-01T00:00:00",\n "price": 21.19,\n "symbol": "MSFT"\n },\n {\n "date": "2006-06-01T00:00:00",\n "price": 21.8,\n "symbol": "MSFT"\n },\n {\n "date": "2006-07-01T00:00:00",\n "price": 22.51,\n "symbol": "MSFT"\n },\n {\n "date": "2006-08-01T00:00:00",\n "price": 24.13,\n "symbol": "MSFT"\n },\n {\n "date": "2006-09-01T00:00:00",\n "price": 25.68,\n "symbol": "MSFT"\n },\n {\n "date": "2006-10-01T00:00:00",\n "price": 26.96,\n "symbol": "MSFT"\n },\n {\n "date": "2006-11-01T00:00:00",\n "price": 27.66,\n "symbol": "MSFT"\n },\n {\n "date": "2006-12-01T00:00:00",\n "price": 28.13,\n "symbol": "MSFT"\n },\n {\n "date": "2007-01-01T00:00:00",\n "price": 29.07,\n "symbol": "MSFT"\n },\n {\n "date": "2007-02-01T00:00:00",\n "price": 26.63,\n "symbol": "MSFT"\n },\n {\n "date": "2007-03-01T00:00:00",\n "price": 26.35,\n "symbol": "MSFT"\n },\n {\n "date": "2007-04-01T00:00:00",\n "price": 28.3,\n "symbol": "MSFT"\n },\n {\n "date": "2007-05-01T00:00:00",\n "price": 29.11,\n "symbol": "MSFT"\n },\n {\n "date": "2007-06-01T00:00:00",\n "price": 27.95,\n "symbol": "MSFT"\n },\n {\n "date": "2007-07-01T00:00:00",\n "price": 27.5,\n "symbol": "MSFT"\n },\n {\n "date": "2007-08-01T00:00:00",\n "price": 27.34,\n "symbol": "MSFT"\n },\n {\n "date": "2007-09-01T00:00:00",\n "price": 28.04,\n "symbol": "MSFT"\n },\n {\n "date": "2007-10-01T00:00:00",\n "price": 35.03,\n "symbol": "MSFT"\n },\n {\n "date": "2007-11-01T00:00:00",\n "price": 32.09,\n "symbol": "MSFT"\n },\n {\n "date": "2007-12-01T00:00:00",\n "price": 34.0,\n "symbol": "MSFT"\n },\n {\n "date": "2008-01-01T00:00:00",\n "price": 31.13,\n "symbol": "MSFT"\n },\n {\n "date": "2008-02-01T00:00:00",\n "price": 26.07,\n "symbol": "MSFT"\n },\n {\n "date": "2008-03-01T00:00:00",\n "price": 27.21,\n "symbol": "MSFT"\n },\n {\n "date": "2008-04-01T00:00:00",\n "price": 27.34,\n "symbol": "MSFT"\n },\n {\n "date": "2008-05-01T00:00:00",\n "price": 27.25,\n "symbol": "MSFT"\n },\n {\n "date": "2008-06-01T00:00:00",\n "price": 26.47,\n "symbol": "MSFT"\n },\n {\n "date": "2008-07-01T00:00:00",\n "price": 24.75,\n "symbol": "MSFT"\n },\n {\n "date": "2008-08-01T00:00:00",\n "price": 26.36,\n "symbol": "MSFT"\n },\n {\n "date": "2008-09-01T00:00:00",\n "price": 25.78,\n "symbol": "MSFT"\n },\n {\n "date": "2008-10-01T00:00:00",\n "price": 21.57,\n "symbol": "MSFT"\n },\n {\n "date": "2008-11-01T00:00:00",\n "price": 19.66,\n "symbol": "MSFT"\n },\n {\n "date": "2008-12-01T00:00:00",\n "price": 18.91,\n "symbol": "MSFT"\n },\n {\n "date": "2009-01-01T00:00:00",\n "price": 16.63,\n "symbol": "MSFT"\n },\n {\n "date": "2009-02-01T00:00:00",\n "price": 15.81,\n "symbol": "MSFT"\n },\n {\n "date": "2009-03-01T00:00:00",\n "price": 17.99,\n "symbol": "MSFT"\n },\n {\n "date": "2009-04-01T00:00:00",\n "price": 19.84,\n "symbol": "MSFT"\n },\n {\n "date": "2009-05-01T00:00:00",\n "price": 20.59,\n "symbol": "MSFT"\n },\n {\n "date": "2009-06-01T00:00:00",\n "price": 23.42,\n "symbol": "MSFT"\n },\n {\n "date": "2009-07-01T00:00:00",\n "price": 23.18,\n "symbol": "MSFT"\n },\n {\n "date": "2009-08-01T00:00:00",\n "price": 24.43,\n "symbol": "MSFT"\n },\n {\n "date": "2009-09-01T00:00:00",\n "price": 25.49,\n "symbol": "MSFT"\n },\n {\n "date": "2009-10-01T00:00:00",\n "price": 27.48,\n "symbol": "MSFT"\n },\n {\n "date": "2009-11-01T00:00:00",\n "price": 29.27,\n "symbol": "MSFT"\n },\n {\n "date": "2009-12-01T00:00:00",\n "price": 30.34,\n "symbol": "MSFT"\n },\n {\n "date": "2010-01-01T00:00:00",\n "price": 28.05,\n "symbol": "MSFT"\n },\n {\n "date": "2010-02-01T00:00:00",\n "price": 28.67,\n "symbol": "MSFT"\n },\n {\n "date": "2010-03-01T00:00:00",\n "price": 28.8,\n "symbol": "MSFT"\n },\n {\n "date": "2000-01-01T00:00:00",\n "price": 64.56,\n "symbol": "AMZN"\n },\n {\n "date": "2000-02-01T00:00:00",\n "price": 68.87,\n "symbol": "AMZN"\n },\n {\n "date": "2000-03-01T00:00:00",\n "price": 67.0,\n "symbol": "AMZN"\n },\n {\n "date": "2000-04-01T00:00:00",\n "price": 55.19,\n "symbol": "AMZN"\n },\n {\n "date": "2000-05-01T00:00:00",\n "price": 48.31,\n "symbol": "AMZN"\n },\n {\n "date": "2000-06-01T00:00:00",\n "price": 36.31,\n "symbol": "AMZN"\n },\n {\n "date": "2000-07-01T00:00:00",\n "price": 30.12,\n "symbol": "AMZN"\n },\n {\n "date": "2000-08-01T00:00:00",\n "price": 41.5,\n "symbol": "AMZN"\n },\n {\n "date": "2000-09-01T00:00:00",\n "price": 38.44,\n "symbol": "AMZN"\n },\n {\n "date": "2000-10-01T00:00:00",\n "price": 36.62,\n "symbol": "AMZN"\n },\n {\n "date": "2000-11-01T00:00:00",\n "price": 24.69,\n "symbol": "AMZN"\n },\n {\n "date": "2000-12-01T00:00:00",\n "price": 15.56,\n "symbol": "AMZN"\n },\n {\n "date": "2001-01-01T00:00:00",\n "price": 17.31,\n "symbol": "AMZN"\n },\n {\n "date": "2001-02-01T00:00:00",\n "price": 10.19,\n "symbol": "AMZN"\n },\n {\n "date": "2001-03-01T00:00:00",\n "price": 10.23,\n "symbol": "AMZN"\n },\n {\n "date": "2001-04-01T00:00:00",\n "price": 15.78,\n "symbol": "AMZN"\n },\n {\n "date": "2001-05-01T00:00:00",\n "price": 16.69,\n "symbol": "AMZN"\n },\n {\n "date": "2001-06-01T00:00:00",\n "price": 14.15,\n "symbol": "AMZN"\n },\n {\n "date": "2001-07-01T00:00:00",\n "price": 12.49,\n "symbol": "AMZN"\n },\n {\n "date": "2001-08-01T00:00:00",\n "price": 8.94,\n "symbol": "AMZN"\n },\n {\n "date": "2001-09-01T00:00:00",\n "price": 5.97,\n "symbol": "AMZN"\n },\n {\n "date": "2001-10-01T00:00:00",\n "price": 6.98,\n "symbol": "AMZN"\n },\n {\n "date": "2001-11-01T00:00:00",\n "price": 11.32,\n "symbol": "AMZN"\n },\n {\n "date": "2001-12-01T00:00:00",\n "price": 10.82,\n "symbol": "AMZN"\n },\n {\n "date": "2002-01-01T00:00:00",\n "price": 14.19,\n "symbol": "AMZN"\n },\n {\n "date": "2002-02-01T00:00:00",\n "price": 14.1,\n "symbol": "AMZN"\n },\n {\n "date": "2002-03-01T00:00:00",\n "price": 14.3,\n "symbol": "AMZN"\n },\n {\n "date": "2002-04-01T00:00:00",\n "price": 16.69,\n "symbol": "AMZN"\n },\n {\n "date": "2002-05-01T00:00:00",\n "price": 18.23,\n "symbol": "AMZN"\n },\n {\n "date": "2002-06-01T00:00:00",\n "price": 16.25,\n "symbol": "AMZN"\n },\n {\n "date": "2002-07-01T00:00:00",\n "price": 14.45,\n "symbol": "AMZN"\n },\n {\n "date": "2002-08-01T00:00:00",\n "price": 14.94,\n "symbol": "AMZN"\n },\n {\n "date": "2002-09-01T00:00:00",\n "price": 15.93,\n "symbol": "AMZN"\n },\n {\n "date": "2002-10-01T00:00:00",\n "price": 19.36,\n "symbol": "AMZN"\n },\n {\n "date": "2002-11-01T00:00:00",\n "price": 23.35,\n "symbol": "AMZN"\n },\n {\n "date": "2002-12-01T00:00:00",\n "price": 18.89,\n "symbol": "AMZN"\n },\n {\n "date": "2003-01-01T00:00:00",\n "price": 21.85,\n "symbol": "AMZN"\n },\n {\n "date": "2003-02-01T00:00:00",\n "price": 22.01,\n "symbol": "AMZN"\n },\n {\n "date": "2003-03-01T00:00:00",\n "price": 26.03,\n "symbol": "AMZN"\n },\n {\n "date": "2003-04-01T00:00:00",\n "price": 28.69,\n "symbol": "AMZN"\n },\n {\n "date": "2003-05-01T00:00:00",\n "price": 35.89,\n "symbol": "AMZN"\n },\n {\n "date": "2003-06-01T00:00:00",\n "price": 36.32,\n "symbol": "AMZN"\n },\n {\n "date": "2003-07-01T00:00:00",\n "price": 41.64,\n "symbol": "AMZN"\n },\n {\n "date": "2003-08-01T00:00:00",\n "price": 46.32,\n "symbol": "AMZN"\n },\n {\n "date": "2003-09-01T00:00:00",\n "price": 48.43,\n "symbol": "AMZN"\n },\n {\n "date": "2003-10-01T00:00:00",\n "price": 54.43,\n "symbol": "AMZN"\n },\n {\n "date": "2003-11-01T00:00:00",\n "price": 53.97,\n "symbol": "AMZN"\n },\n {\n "date": "2003-12-01T00:00:00",\n "price": 52.62,\n "symbol": "AMZN"\n },\n {\n "date": "2004-01-01T00:00:00",\n "price": 50.4,\n "symbol": "AMZN"\n },\n {\n "date": "2004-02-01T00:00:00",\n "price": 43.01,\n "symbol": "AMZN"\n },\n {\n "date": "2004-03-01T00:00:00",\n "price": 43.28,\n "symbol": "AMZN"\n },\n {\n "date": "2004-04-01T00:00:00",\n "price": 43.6,\n "symbol": "AMZN"\n },\n {\n "date": "2004-05-01T00:00:00",\n "price": 48.5,\n "symbol": "AMZN"\n },\n {\n "date": "2004-06-01T00:00:00",\n "price": 54.4,\n "symbol": "AMZN"\n },\n {\n "date": "2004-07-01T00:00:00",\n "price": 38.92,\n "symbol": "AMZN"\n },\n {\n "date": "2004-08-01T00:00:00",\n "price": 38.14,\n "symbol": "AMZN"\n },\n {\n "date": "2004-09-01T00:00:00",\n "price": 40.86,\n "symbol": "AMZN"\n },\n {\n "date": "2004-10-01T00:00:00",\n "price": 34.13,\n "symbol": "AMZN"\n },\n {\n "date": "2004-11-01T00:00:00",\n "price": 39.68,\n "symbol": "AMZN"\n },\n {\n "date": "2004-12-01T00:00:00",\n "price": 44.29,\n "symbol": "AMZN"\n },\n {\n "date": "2005-01-01T00:00:00",\n "price": 43.22,\n "symbol": "AMZN"\n },\n {\n "date": "2005-02-01T00:00:00",\n "price": 35.18,\n "symbol": "AMZN"\n },\n {\n "date": "2005-03-01T00:00:00",\n "price": 34.27,\n "symbol": "AMZN"\n },\n {\n "date": "2005-04-01T00:00:00",\n "price": 32.36,\n "symbol": "AMZN"\n },\n {\n "date": "2005-05-01T00:00:00",\n "price": 35.51,\n "symbol": "AMZN"\n },\n {\n "date": "2005-06-01T00:00:00",\n "price": 33.09,\n "symbol": "AMZN"\n },\n {\n "date": "2005-07-01T00:00:00",\n "price": 45.15,\n "symbol": "AMZN"\n },\n {\n "date": "2005-08-01T00:00:00",\n "price": 42.7,\n "symbol": "AMZN"\n },\n {\n "date": "2005-09-01T00:00:00",\n "price": 45.3,\n "symbol": "AMZN"\n },\n {\n "date": "2005-10-01T00:00:00",\n "price": 39.86,\n "symbol": "AMZN"\n },\n {\n "date": "2005-11-01T00:00:00",\n "price": 48.46,\n "symbol": "AMZN"\n },\n {\n "date": "2005-12-01T00:00:00",\n "price": 47.15,\n "symbol": "AMZN"\n },\n {\n "date": "2006-01-01T00:00:00",\n "price": 44.82,\n "symbol": "AMZN"\n },\n {\n "date": "2006-02-01T00:00:00",\n "price": 37.44,\n "symbol": "AMZN"\n },\n {\n "date": "2006-03-01T00:00:00",\n "price": 36.53,\n "symbol": "AMZN"\n },\n {\n "date": "2006-04-01T00:00:00",\n "price": 35.21,\n "symbol": "AMZN"\n },\n {\n "date": "2006-05-01T00:00:00",\n "price": 34.61,\n "symbol": "AMZN"\n },\n {\n "date": "2006-06-01T00:00:00",\n "price": 38.68,\n "symbol": "AMZN"\n },\n {\n "date": "2006-07-01T00:00:00",\n "price": 26.89,\n "symbol": "AMZN"\n },\n {\n "date": "2006-08-01T00:00:00",\n "price": 30.83,\n "symbol": "AMZN"\n },\n {\n "date": "2006-09-01T00:00:00",\n "price": 32.12,\n "symbol": "AMZN"\n },\n {\n "date": "2006-10-01T00:00:00",\n "price": 38.09,\n "symbol": "AMZN"\n },\n {\n "date": "2006-11-01T00:00:00",\n "price": 40.34,\n "symbol": "AMZN"\n },\n {\n "date": "2006-12-01T00:00:00",\n "price": 39.46,\n "symbol": "AMZN"\n },\n {\n "date": "2007-01-01T00:00:00",\n "price": 37.67,\n "symbol": "AMZN"\n },\n {\n "date": "2007-02-01T00:00:00",\n "price": 39.14,\n "symbol": "AMZN"\n },\n {\n "date": "2007-03-01T00:00:00",\n "price": 39.79,\n "symbol": "AMZN"\n },\n {\n "date": "2007-04-01T00:00:00",\n "price": 61.33,\n "symbol": "AMZN"\n },\n {\n "date": "2007-05-01T00:00:00",\n "price": 69.14,\n "symbol": "AMZN"\n },\n {\n "date": "2007-06-01T00:00:00",\n "price": 68.41,\n "symbol": "AMZN"\n },\n {\n "date": "2007-07-01T00:00:00",\n "price": 78.54,\n "symbol": "AMZN"\n },\n {\n "date": "2007-08-01T00:00:00",\n "price": 79.91,\n "symbol": "AMZN"\n },\n {\n "date": "2007-09-01T00:00:00",\n "price": 93.15,\n "symbol": "AMZN"\n },\n {\n "date": "2007-10-01T00:00:00",\n "price": 89.15,\n "symbol": "AMZN"\n },\n {\n "date": "2007-11-01T00:00:00",\n "price": 90.56,\n "symbol": "AMZN"\n },\n {\n "date": "2007-12-01T00:00:00",\n "price": 92.64,\n "symbol": "AMZN"\n },\n {\n "date": "2008-01-01T00:00:00",\n "price": 77.7,\n "symbol": "AMZN"\n },\n {\n "date": "2008-02-01T00:00:00",\n "price": 64.47,\n "symbol": "AMZN"\n },\n {\n "date": "2008-03-01T00:00:00",\n "price": 71.3,\n "symbol": "AMZN"\n },\n {\n "date": "2008-04-01T00:00:00",\n "price": 78.63,\n "symbol": "AMZN"\n },\n {\n "date": "2008-05-01T00:00:00",\n "price": 81.62,\n "symbol": "AMZN"\n },\n {\n "date": "2008-06-01T00:00:00",\n "price": 73.33,\n "symbol": "AMZN"\n },\n {\n "date": "2008-07-01T00:00:00",\n "price": 76.34,\n "symbol": "AMZN"\n },\n {\n "date": "2008-08-01T00:00:00",\n "price": 80.81,\n "symbol": "AMZN"\n },\n {\n "date": "2008-09-01T00:00:00",\n "price": 72.76,\n "symbol": "AMZN"\n },\n {\n "date": "2008-10-01T00:00:00",\n "price": 57.24,\n "symbol": "AMZN"\n },\n {\n "date": "2008-11-01T00:00:00",\n "price": 42.7,\n "symbol": "AMZN"\n },\n {\n "date": "2008-12-01T00:00:00",\n "price": 51.28,\n "symbol": "AMZN"\n },\n {\n "date": "2009-01-01T00:00:00",\n "price": 58.82,\n "symbol": "AMZN"\n },\n {\n "date": "2009-02-01T00:00:00",\n "price": 64.79,\n "symbol": "AMZN"\n },\n {\n "date": "2009-03-01T00:00:00",\n "price": 73.44,\n "symbol": "AMZN"\n },\n {\n "date": "2009-04-01T00:00:00",\n "price": 80.52,\n "symbol": "AMZN"\n },\n {\n "date": "2009-05-01T00:00:00",\n "price": 77.99,\n "symbol": "AMZN"\n },\n {\n "date": "2009-06-01T00:00:00",\n "price": 83.66,\n "symbol": "AMZN"\n },\n {\n "date": "2009-07-01T00:00:00",\n "price": 85.76,\n "symbol": "AMZN"\n },\n {\n "date": "2009-08-01T00:00:00",\n "price": 81.19,\n "symbol": "AMZN"\n },\n {\n "date": "2009-09-01T00:00:00",\n "price": 93.36,\n "symbol": "AMZN"\n },\n {\n "date": "2009-10-01T00:00:00",\n "price": 118.81,\n "symbol": "AMZN"\n },\n {\n "date": "2009-11-01T00:00:00",\n "price": 135.91,\n "symbol": "AMZN"\n },\n {\n "date": "2009-12-01T00:00:00",\n "price": 134.52,\n "symbol": "AMZN"\n },\n {\n "date": "2010-01-01T00:00:00",\n "price": 125.41,\n "symbol": "AMZN"\n },\n {\n "date": "2010-02-01T00:00:00",\n "price": 118.4,\n "symbol": "AMZN"\n },\n {\n "date": "2010-03-01T00:00:00",\n "price": 128.82,\n "symbol": "AMZN"\n },\n {\n "date": "2000-01-01T00:00:00",\n "price": 100.52,\n "symbol": "IBM"\n },\n {\n "date": "2000-02-01T00:00:00",\n "price": 92.11,\n "symbol": "IBM"\n },\n {\n "date": "2000-03-01T00:00:00",\n "price": 106.11,\n "symbol": "IBM"\n },\n {\n "date": "2000-04-01T00:00:00",\n "price": 99.95,\n "symbol": "IBM"\n },\n {\n "date": "2000-05-01T00:00:00",\n "price": 96.31,\n "symbol": "IBM"\n },\n {\n "date": "2000-06-01T00:00:00",\n "price": 98.33,\n "symbol": "IBM"\n },\n {\n "date": "2000-07-01T00:00:00",\n "price": 100.74,\n "symbol": "IBM"\n },\n {\n "date": "2000-08-01T00:00:00",\n "price": 118.62,\n "symbol": "IBM"\n },\n {\n "date": "2000-09-01T00:00:00",\n "price": 101.19,\n "symbol": "IBM"\n },\n {\n "date": "2000-10-01T00:00:00",\n "price": 88.5,\n "symbol": "IBM"\n },\n {\n "date": "2000-11-01T00:00:00",\n "price": 84.12,\n "symbol": "IBM"\n },\n {\n "date": "2000-12-01T00:00:00",\n "price": 76.47,\n "symbol": "IBM"\n },\n {\n "date": "2001-01-01T00:00:00",\n "price": 100.76,\n "symbol": "IBM"\n },\n {\n "date": "2001-02-01T00:00:00",\n "price": 89.98,\n "symbol": "IBM"\n },\n {\n "date": "2001-03-01T00:00:00",\n "price": 86.63,\n "symbol": "IBM"\n },\n {\n "date": "2001-04-01T00:00:00",\n "price": 103.7,\n "symbol": "IBM"\n },\n {\n "date": "2001-05-01T00:00:00",\n "price": 100.82,\n "symbol": "IBM"\n },\n {\n "date": "2001-06-01T00:00:00",\n "price": 102.35,\n "symbol": "IBM"\n },\n {\n "date": "2001-07-01T00:00:00",\n "price": 94.87,\n "symbol": "IBM"\n },\n {\n "date": "2001-08-01T00:00:00",\n "price": 90.25,\n "symbol": "IBM"\n },\n {\n "date": "2001-09-01T00:00:00",\n "price": 82.82,\n "symbol": "IBM"\n },\n {\n "date": "2001-10-01T00:00:00",\n "price": 97.58,\n "symbol": "IBM"\n },\n {\n "date": "2001-11-01T00:00:00",\n "price": 104.5,\n "symbol": "IBM"\n },\n {\n "date": "2001-12-01T00:00:00",\n "price": 109.36,\n "symbol": "IBM"\n },\n {\n "date": "2002-01-01T00:00:00",\n "price": 97.54,\n "symbol": "IBM"\n },\n {\n "date": "2002-02-01T00:00:00",\n "price": 88.82,\n "symbol": "IBM"\n },\n {\n "date": "2002-03-01T00:00:00",\n "price": 94.15,\n "symbol": "IBM"\n },\n {\n "date": "2002-04-01T00:00:00",\n "price": 75.82,\n "symbol": "IBM"\n },\n {\n "date": "2002-05-01T00:00:00",\n "price": 72.97,\n "symbol": "IBM"\n },\n {\n "date": "2002-06-01T00:00:00",\n "price": 65.31,\n "symbol": "IBM"\n },\n {\n "date": "2002-07-01T00:00:00",\n "price": 63.86,\n "symbol": "IBM"\n },\n {\n "date": "2002-08-01T00:00:00",\n "price": 68.52,\n "symbol": "IBM"\n },\n {\n "date": "2002-09-01T00:00:00",\n "price": 53.01,\n "symbol": "IBM"\n },\n {\n "date": "2002-10-01T00:00:00",\n "price": 71.76,\n "symbol": "IBM"\n },\n {\n "date": "2002-11-01T00:00:00",\n "price": 79.16,\n "symbol": "IBM"\n },\n {\n "date": "2002-12-01T00:00:00",\n "price": 70.58,\n "symbol": "IBM"\n },\n {\n "date": "2003-01-01T00:00:00",\n "price": 71.22,\n "symbol": "IBM"\n },\n {\n "date": "2003-02-01T00:00:00",\n "price": 71.13,\n "symbol": "IBM"\n },\n {\n "date": "2003-03-01T00:00:00",\n "price": 71.57,\n "symbol": "IBM"\n },\n {\n "date": "2003-04-01T00:00:00",\n "price": 77.47,\n "symbol": "IBM"\n },\n {\n "date": "2003-05-01T00:00:00",\n "price": 80.48,\n "symbol": "IBM"\n },\n {\n "date": "2003-06-01T00:00:00",\n "price": 75.42,\n "symbol": "IBM"\n },\n {\n "date": "2003-07-01T00:00:00",\n "price": 74.28,\n "symbol": "IBM"\n },\n {\n "date": "2003-08-01T00:00:00",\n "price": 75.12,\n "symbol": "IBM"\n },\n {\n "date": "2003-09-01T00:00:00",\n "price": 80.91,\n "symbol": "IBM"\n },\n {\n "date": "2003-10-01T00:00:00",\n "price": 81.96,\n "symbol": "IBM"\n },\n {\n "date": "2003-11-01T00:00:00",\n "price": 83.08,\n "symbol": "IBM"\n },\n {\n "date": "2003-12-01T00:00:00",\n "price": 85.05,\n "symbol": "IBM"\n },\n {\n "date": "2004-01-01T00:00:00",\n "price": 91.06,\n "symbol": "IBM"\n },\n {\n "date": "2004-02-01T00:00:00",\n "price": 88.7,\n "symbol": "IBM"\n },\n {\n "date": "2004-03-01T00:00:00",\n "price": 84.41,\n "symbol": "IBM"\n },\n {\n "date": "2004-04-01T00:00:00",\n "price": 81.04,\n "symbol": "IBM"\n },\n {\n "date": "2004-05-01T00:00:00",\n "price": 81.59,\n "symbol": "IBM"\n },\n {\n "date": "2004-06-01T00:00:00",\n "price": 81.19,\n "symbol": "IBM"\n },\n {\n "date": "2004-07-01T00:00:00",\n "price": 80.19,\n "symbol": "IBM"\n },\n {\n "date": "2004-08-01T00:00:00",\n "price": 78.17,\n "symbol": "IBM"\n },\n {\n "date": "2004-09-01T00:00:00",\n "price": 79.13,\n "symbol": "IBM"\n },\n {\n "date": "2004-10-01T00:00:00",\n "price": 82.84,\n "symbol": "IBM"\n },\n {\n "date": "2004-11-01T00:00:00",\n "price": 87.15,\n "symbol": "IBM"\n },\n {\n "date": "2004-12-01T00:00:00",\n "price": 91.16,\n "symbol": "IBM"\n },\n {\n "date": "2005-01-01T00:00:00",\n "price": 86.39,\n "symbol": "IBM"\n },\n {\n "date": "2005-02-01T00:00:00",\n "price": 85.78,\n "symbol": "IBM"\n },\n {\n "date": "2005-03-01T00:00:00",\n "price": 84.66,\n "symbol": "IBM"\n },\n {\n "date": "2005-04-01T00:00:00",\n "price": 70.77,\n "symbol": "IBM"\n },\n {\n "date": "2005-05-01T00:00:00",\n "price": 70.18,\n "symbol": "IBM"\n },\n {\n "date": "2005-06-01T00:00:00",\n "price": 68.93,\n "symbol": "IBM"\n },\n {\n "date": "2005-07-01T00:00:00",\n "price": 77.53,\n "symbol": "IBM"\n },\n {\n "date": "2005-08-01T00:00:00",\n "price": 75.07,\n "symbol": "IBM"\n },\n {\n "date": "2005-09-01T00:00:00",\n "price": 74.7,\n "symbol": "IBM"\n },\n {\n "date": "2005-10-01T00:00:00",\n "price": 76.25,\n "symbol": "IBM"\n },\n {\n "date": "2005-11-01T00:00:00",\n "price": 82.98,\n "symbol": "IBM"\n },\n {\n "date": "2005-12-01T00:00:00",\n "price": 76.73,\n "symbol": "IBM"\n },\n {\n "date": "2006-01-01T00:00:00",\n "price": 75.89,\n "symbol": "IBM"\n },\n {\n "date": "2006-02-01T00:00:00",\n "price": 75.09,\n "symbol": "IBM"\n },\n {\n "date": "2006-03-01T00:00:00",\n "price": 77.17,\n "symbol": "IBM"\n },\n {\n "date": "2006-04-01T00:00:00",\n "price": 77.05,\n "symbol": "IBM"\n },\n {\n "date": "2006-05-01T00:00:00",\n "price": 75.04,\n "symbol": "IBM"\n },\n {\n "date": "2006-06-01T00:00:00",\n "price": 72.15,\n "symbol": "IBM"\n },\n {\n "date": "2006-07-01T00:00:00",\n "price": 72.7,\n "symbol": "IBM"\n },\n {\n "date": "2006-08-01T00:00:00",\n "price": 76.35,\n "symbol": "IBM"\n },\n {\n "date": "2006-09-01T00:00:00",\n "price": 77.26,\n "symbol": "IBM"\n },\n {\n "date": "2006-10-01T00:00:00",\n "price": 87.06,\n "symbol": "IBM"\n },\n {\n "date": "2006-11-01T00:00:00",\n "price": 86.95,\n "symbol": "IBM"\n },\n {\n "date": "2006-12-01T00:00:00",\n "price": 91.9,\n "symbol": "IBM"\n },\n {\n "date": "2007-01-01T00:00:00",\n "price": 93.79,\n "symbol": "IBM"\n },\n {\n "date": "2007-02-01T00:00:00",\n "price": 88.18,\n "symbol": "IBM"\n },\n {\n "date": "2007-03-01T00:00:00",\n "price": 89.44,\n "symbol": "IBM"\n },\n {\n "date": "2007-04-01T00:00:00",\n "price": 96.98,\n "symbol": "IBM"\n },\n {\n "date": "2007-05-01T00:00:00",\n "price": 101.54,\n "symbol": "IBM"\n },\n {\n "date": "2007-06-01T00:00:00",\n "price": 100.25,\n "symbol": "IBM"\n },\n {\n "date": "2007-07-01T00:00:00",\n "price": 105.4,\n "symbol": "IBM"\n },\n {\n "date": "2007-08-01T00:00:00",\n "price": 111.54,\n "symbol": "IBM"\n },\n {\n "date": "2007-09-01T00:00:00",\n "price": 112.6,\n "symbol": "IBM"\n },\n {\n "date": "2007-10-01T00:00:00",\n "price": 111.0,\n "symbol": "IBM"\n },\n {\n "date": "2007-11-01T00:00:00",\n "price": 100.9,\n "symbol": "IBM"\n },\n {\n "date": "2007-12-01T00:00:00",\n "price": 103.7,\n "symbol": "IBM"\n },\n {\n "date": "2008-01-01T00:00:00",\n "price": 102.75,\n "symbol": "IBM"\n },\n {\n "date": "2008-02-01T00:00:00",\n "price": 109.64,\n "symbol": "IBM"\n },\n {\n "date": "2008-03-01T00:00:00",\n "price": 110.87,\n "symbol": "IBM"\n },\n {\n "date": "2008-04-01T00:00:00",\n "price": 116.23,\n "symbol": "IBM"\n },\n {\n "date": "2008-05-01T00:00:00",\n "price": 125.14,\n "symbol": "IBM"\n },\n {\n "date": "2008-06-01T00:00:00",\n "price": 114.6,\n "symbol": "IBM"\n },\n {\n "date": "2008-07-01T00:00:00",\n "price": 123.74,\n "symbol": "IBM"\n },\n {\n "date": "2008-08-01T00:00:00",\n "price": 118.16,\n "symbol": "IBM"\n },\n {\n "date": "2008-09-01T00:00:00",\n "price": 113.53,\n "symbol": "IBM"\n },\n {\n "date": "2008-10-01T00:00:00",\n "price": 90.24,\n "symbol": "IBM"\n },\n {\n "date": "2008-11-01T00:00:00",\n "price": 79.65,\n "symbol": "IBM"\n },\n {\n "date": "2008-12-01T00:00:00",\n "price": 82.15,\n "symbol": "IBM"\n },\n {\n "date": "2009-01-01T00:00:00",\n "price": 89.46,\n "symbol": "IBM"\n },\n {\n "date": "2009-02-01T00:00:00",\n "price": 90.32,\n "symbol": "IBM"\n },\n {\n "date": "2009-03-01T00:00:00",\n "price": 95.09,\n "symbol": "IBM"\n },\n {\n "date": "2009-04-01T00:00:00",\n "price": 101.29,\n "symbol": "IBM"\n },\n {\n "date": "2009-05-01T00:00:00",\n "price": 104.85,\n "symbol": "IBM"\n },\n {\n "date": "2009-06-01T00:00:00",\n "price": 103.01,\n "symbol": "IBM"\n },\n {\n "date": "2009-07-01T00:00:00",\n "price": 116.34,\n "symbol": "IBM"\n },\n {\n "date": "2009-08-01T00:00:00",\n "price": 117.0,\n "symbol": "IBM"\n },\n {\n "date": "2009-09-01T00:00:00",\n "price": 118.55,\n "symbol": "IBM"\n },\n {\n "date": "2009-10-01T00:00:00",\n "price": 119.54,\n "symbol": "IBM"\n },\n {\n "date": "2009-11-01T00:00:00",\n "price": 125.79,\n "symbol": "IBM"\n },\n {\n "date": "2009-12-01T00:00:00",\n "price": 130.32,\n "symbol": "IBM"\n },\n {\n "date": "2010-01-01T00:00:00",\n "price": 121.85,\n "symbol": "IBM"\n },\n {\n "date": "2010-02-01T00:00:00",\n "price": 127.16,\n "symbol": "IBM"\n },\n {\n "date": "2010-03-01T00:00:00",\n "price": 125.55,\n "symbol": "IBM"\n },\n {\n "date": "2004-08-01T00:00:00",\n "price": 102.37,\n "symbol": "GOOG"\n },\n {\n "date": "2004-09-01T00:00:00",\n "price": 129.6,\n "symbol": "GOOG"\n },\n {\n "date": "2004-10-01T00:00:00",\n "price": 190.64,\n "symbol": "GOOG"\n },\n {\n "date": "2004-11-01T00:00:00",\n "price": 181.98,\n "symbol": "GOOG"\n },\n {\n "date": "2004-12-01T00:00:00",\n "price": 192.79,\n "symbol": "GOOG"\n },\n {\n "date": "2005-01-01T00:00:00",\n "price": 195.62,\n "symbol": "GOOG"\n },\n {\n "date": "2005-02-01T00:00:00",\n "price": 187.99,\n "symbol": "GOOG"\n },\n {\n "date": "2005-03-01T00:00:00",\n "price": 180.51,\n "symbol": "GOOG"\n },\n {\n "date": "2005-04-01T00:00:00",\n "price": 220.0,\n "symbol": "GOOG"\n },\n {\n "date": "2005-05-01T00:00:00",\n "price": 277.27,\n "symbol": "GOOG"\n },\n {\n "date": "2005-06-01T00:00:00",\n "price": 294.15,\n "symbol": "GOOG"\n },\n {\n "date": "2005-07-01T00:00:00",\n "price": 287.76,\n "symbol": "GOOG"\n },\n {\n "date": "2005-08-01T00:00:00",\n "price": 286.0,\n "symbol": "GOOG"\n },\n {\n "date": "2005-09-01T00:00:00",\n "price": 316.46,\n "symbol": "GOOG"\n },\n {\n "date": "2005-10-01T00:00:00",\n "price": 372.14,\n "symbol": "GOOG"\n },\n {\n "date": "2005-11-01T00:00:00",\n "price": 404.91,\n "symbol": "GOOG"\n },\n {\n "date": "2005-12-01T00:00:00",\n "price": 414.86,\n "symbol": "GOOG"\n },\n {\n "date": "2006-01-01T00:00:00",\n "price": 432.66,\n "symbol": "GOOG"\n },\n {\n "date": "2006-02-01T00:00:00",\n "price": 362.62,\n "symbol": "GOOG"\n },\n {\n "date": "2006-03-01T00:00:00",\n "price": 390.0,\n "symbol": "GOOG"\n },\n {\n "date": "2006-04-01T00:00:00",\n "price": 417.94,\n "symbol": "GOOG"\n },\n {\n "date": "2006-05-01T00:00:00",\n "price": 371.82,\n "symbol": "GOOG"\n },\n {\n "date": "2006-06-01T00:00:00",\n "price": 419.33,\n "symbol": "GOOG"\n },\n {\n "date": "2006-07-01T00:00:00",\n "price": 386.6,\n "symbol": "GOOG"\n },\n {\n "date": "2006-08-01T00:00:00",\n "price": 378.53,\n "symbol": "GOOG"\n },\n {\n "date": "2006-09-01T00:00:00",\n "price": 401.9,\n "symbol": "GOOG"\n },\n {\n "date": "2006-10-01T00:00:00",\n "price": 476.39,\n "symbol": "GOOG"\n },\n {\n "date": "2006-11-01T00:00:00",\n "price": 484.81,\n "symbol": "GOOG"\n },\n {\n "date": "2006-12-01T00:00:00",\n "price": 460.48,\n "symbol": "GOOG"\n },\n {\n "date": "2007-01-01T00:00:00",\n "price": 501.5,\n "symbol": "GOOG"\n },\n {\n "date": "2007-02-01T00:00:00",\n "price": 449.45,\n "symbol": "GOOG"\n },\n {\n "date": "2007-03-01T00:00:00",\n "price": 458.16,\n "symbol": "GOOG"\n },\n {\n "date": "2007-04-01T00:00:00",\n "price": 471.38,\n "symbol": "GOOG"\n },\n {\n "date": "2007-05-01T00:00:00",\n "price": 497.91,\n "symbol": "GOOG"\n },\n {\n "date": "2007-06-01T00:00:00",\n "price": 522.7,\n "symbol": "GOOG"\n },\n {\n "date": "2007-07-01T00:00:00",\n "price": 510.0,\n "symbol": "GOOG"\n },\n {\n "date": "2007-08-01T00:00:00",\n "price": 515.25,\n "symbol": "GOOG"\n },\n {\n "date": "2007-09-01T00:00:00",\n "price": 567.27,\n "symbol": "GOOG"\n },\n {\n "date": "2007-10-01T00:00:00",\n "price": 707.0,\n "symbol": "GOOG"\n },\n {\n "date": "2007-11-01T00:00:00",\n "price": 693.0,\n "symbol": "GOOG"\n },\n {\n "date": "2007-12-01T00:00:00",\n "price": 691.48,\n "symbol": "GOOG"\n },\n {\n "date": "2008-01-01T00:00:00",\n "price": 564.3,\n "symbol": "GOOG"\n },\n {\n "date": "2008-02-01T00:00:00",\n "price": 471.18,\n "symbol": "GOOG"\n },\n {\n "date": "2008-03-01T00:00:00",\n "price": 440.47,\n "symbol": "GOOG"\n },\n {\n "date": "2008-04-01T00:00:00",\n "price": 574.29,\n "symbol": "GOOG"\n },\n {\n "date": "2008-05-01T00:00:00",\n "price": 585.8,\n "symbol": "GOOG"\n },\n {\n "date": "2008-06-01T00:00:00",\n "price": 526.42,\n "symbol": "GOOG"\n },\n {\n "date": "2008-07-01T00:00:00",\n "price": 473.75,\n "symbol": "GOOG"\n },\n {\n "date": "2008-08-01T00:00:00",\n "price": 463.29,\n "symbol": "GOOG"\n },\n {\n "date": "2008-09-01T00:00:00",\n "price": 400.52,\n "symbol": "GOOG"\n },\n {\n "date": "2008-10-01T00:00:00",\n "price": 359.36,\n "symbol": "GOOG"\n },\n {\n "date": "2008-11-01T00:00:00",\n "price": 292.96,\n "symbol": "GOOG"\n },\n {\n "date": "2008-12-01T00:00:00",\n "price": 307.65,\n "symbol": "GOOG"\n },\n {\n "date": "2009-01-01T00:00:00",\n "price": 338.53,\n "symbol": "GOOG"\n },\n {\n "date": "2009-02-01T00:00:00",\n "price": 337.99,\n "symbol": "GOOG"\n },\n {\n "date": "2009-03-01T00:00:00",\n "price": 348.06,\n "symbol": "GOOG"\n },\n {\n "date": "2009-04-01T00:00:00",\n "price": 395.97,\n "symbol": "GOOG"\n },\n {\n "date": "2009-05-01T00:00:00",\n "price": 417.23,\n "symbol": "GOOG"\n },\n {\n "date": "2009-06-01T00:00:00",\n "price": 421.59,\n "symbol": "GOOG"\n },\n {\n "date": "2009-07-01T00:00:00",\n "price": 443.05,\n "symbol": "GOOG"\n },\n {\n "date": "2009-08-01T00:00:00",\n "price": 461.67,\n "symbol": "GOOG"\n },\n {\n "date": "2009-09-01T00:00:00",\n "price": 495.85,\n "symbol": "GOOG"\n },\n {\n "date": "2009-10-01T00:00:00",\n "price": 536.12,\n "symbol": "GOOG"\n },\n {\n "date": "2009-11-01T00:00:00",\n "price": 583.0,\n "symbol": "GOOG"\n },\n {\n "date": "2009-12-01T00:00:00",\n "price": 619.98,\n "symbol": "GOOG"\n },\n {\n "date": "2010-01-01T00:00:00",\n "price": 529.94,\n "symbol": "GOOG"\n },\n {\n "date": "2010-02-01T00:00:00",\n "price": 526.8,\n "symbol": "GOOG"\n },\n {\n "date": "2010-03-01T00:00:00",\n "price": 560.19,\n "symbol": "GOOG"\n },\n {\n "date": "2000-01-01T00:00:00",\n "price": 25.94,\n "symbol": "AAPL"\n },\n {\n "date": "2000-02-01T00:00:00",\n "price": 28.66,\n "symbol": "AAPL"\n },\n {\n "date": "2000-03-01T00:00:00",\n "price": 33.95,\n "symbol": "AAPL"\n },\n {\n "date": "2000-04-01T00:00:00",\n "price": 31.01,\n "symbol": "AAPL"\n },\n {\n "date": "2000-05-01T00:00:00",\n "price": 21.0,\n "symbol": "AAPL"\n },\n {\n "date": "2000-06-01T00:00:00",\n "price": 26.19,\n "symbol": "AAPL"\n },\n {\n "date": "2000-07-01T00:00:00",\n "price": 25.41,\n "symbol": "AAPL"\n },\n {\n "date": "2000-08-01T00:00:00",\n "price": 30.47,\n "symbol": "AAPL"\n },\n {\n "date": "2000-09-01T00:00:00",\n "price": 12.88,\n "symbol": "AAPL"\n },\n {\n "date": "2000-10-01T00:00:00",\n "price": 9.78,\n "symbol": "AAPL"\n },\n {\n "date": "2000-11-01T00:00:00",\n "price": 8.25,\n "symbol": "AAPL"\n },\n {\n "date": "2000-12-01T00:00:00",\n "price": 7.44,\n "symbol": "AAPL"\n },\n {\n "date": "2001-01-01T00:00:00",\n "price": 10.81,\n "symbol": "AAPL"\n },\n {\n "date": "2001-02-01T00:00:00",\n "price": 9.12,\n "symbol": "AAPL"\n },\n {\n "date": "2001-03-01T00:00:00",\n "price": 11.03,\n "symbol": "AAPL"\n },\n {\n "date": "2001-04-01T00:00:00",\n "price": 12.74,\n "symbol": "AAPL"\n },\n {\n "date": "2001-05-01T00:00:00",\n "price": 9.98,\n "symbol": "AAPL"\n },\n {\n "date": "2001-06-01T00:00:00",\n "price": 11.62,\n "symbol": "AAPL"\n },\n {\n "date": "2001-07-01T00:00:00",\n "price": 9.4,\n "symbol": "AAPL"\n },\n {\n "date": "2001-08-01T00:00:00",\n "price": 9.27,\n "symbol": "AAPL"\n },\n {\n "date": "2001-09-01T00:00:00",\n "price": 7.76,\n "symbol": "AAPL"\n },\n {\n "date": "2001-10-01T00:00:00",\n "price": 8.78,\n "symbol": "AAPL"\n },\n {\n "date": "2001-11-01T00:00:00",\n "price": 10.65,\n "symbol": "AAPL"\n },\n {\n "date": "2001-12-01T00:00:00",\n "price": 10.95,\n "symbol": "AAPL"\n },\n {\n "date": "2002-01-01T00:00:00",\n "price": 12.36,\n "symbol": "AAPL"\n },\n {\n "date": "2002-02-01T00:00:00",\n "price": 10.85,\n "symbol": "AAPL"\n },\n {\n "date": "2002-03-01T00:00:00",\n "price": 11.84,\n "symbol": "AAPL"\n },\n {\n "date": "2002-04-01T00:00:00",\n "price": 12.14,\n "symbol": "AAPL"\n },\n {\n "date": "2002-05-01T00:00:00",\n "price": 11.65,\n "symbol": "AAPL"\n },\n {\n "date": "2002-06-01T00:00:00",\n "price": 8.86,\n "symbol": "AAPL"\n },\n {\n "date": "2002-07-01T00:00:00",\n "price": 7.63,\n "symbol": "AAPL"\n },\n {\n "date": "2002-08-01T00:00:00",\n "price": 7.38,\n "symbol": "AAPL"\n },\n {\n "date": "2002-09-01T00:00:00",\n "price": 7.25,\n "symbol": "AAPL"\n },\n {\n "date": "2002-10-01T00:00:00",\n "price": 8.03,\n "symbol": "AAPL"\n },\n {\n "date": "2002-11-01T00:00:00",\n "price": 7.75,\n "symbol": "AAPL"\n },\n {\n "date": "2002-12-01T00:00:00",\n "price": 7.16,\n "symbol": "AAPL"\n },\n {\n "date": "2003-01-01T00:00:00",\n "price": 7.18,\n "symbol": "AAPL"\n },\n {\n "date": "2003-02-01T00:00:00",\n "price": 7.51,\n "symbol": "AAPL"\n },\n {\n "date": "2003-03-01T00:00:00",\n "price": 7.07,\n "symbol": "AAPL"\n },\n {\n "date": "2003-04-01T00:00:00",\n "price": 7.11,\n "symbol": "AAPL"\n },\n {\n "date": "2003-05-01T00:00:00",\n "price": 8.98,\n "symbol": "AAPL"\n },\n {\n "date": "2003-06-01T00:00:00",\n "price": 9.53,\n "symbol": "AAPL"\n },\n {\n "date": "2003-07-01T00:00:00",\n "price": 10.54,\n "symbol": "AAPL"\n },\n {\n "date": "2003-08-01T00:00:00",\n "price": 11.31,\n "symbol": "AAPL"\n },\n {\n "date": "2003-09-01T00:00:00",\n "price": 10.36,\n "symbol": "AAPL"\n },\n {\n "date": "2003-10-01T00:00:00",\n "price": 11.44,\n "symbol": "AAPL"\n },\n {\n "date": "2003-11-01T00:00:00",\n "price": 10.45,\n "symbol": "AAPL"\n },\n {\n "date": "2003-12-01T00:00:00",\n "price": 10.69,\n "symbol": "AAPL"\n },\n {\n "date": "2004-01-01T00:00:00",\n "price": 11.28,\n "symbol": "AAPL"\n },\n {\n "date": "2004-02-01T00:00:00",\n "price": 11.96,\n "symbol": "AAPL"\n },\n {\n "date": "2004-03-01T00:00:00",\n "price": 13.52,\n "symbol": "AAPL"\n },\n {\n "date": "2004-04-01T00:00:00",\n "price": 12.89,\n "symbol": "AAPL"\n },\n {\n "date": "2004-05-01T00:00:00",\n "price": 14.03,\n "symbol": "AAPL"\n },\n {\n "date": "2004-06-01T00:00:00",\n "price": 16.27,\n "symbol": "AAPL"\n },\n {\n "date": "2004-07-01T00:00:00",\n "price": 16.17,\n "symbol": "AAPL"\n },\n {\n "date": "2004-08-01T00:00:00",\n "price": 17.25,\n "symbol": "AAPL"\n },\n {\n "date": "2004-09-01T00:00:00",\n "price": 19.38,\n "symbol": "AAPL"\n },\n {\n "date": "2004-10-01T00:00:00",\n "price": 26.2,\n "symbol": "AAPL"\n },\n {\n "date": "2004-11-01T00:00:00",\n "price": 33.53,\n "symbol": "AAPL"\n },\n {\n "date": "2004-12-01T00:00:00",\n "price": 32.2,\n "symbol": "AAPL"\n },\n {\n "date": "2005-01-01T00:00:00",\n "price": 38.45,\n "symbol": "AAPL"\n },\n {\n "date": "2005-02-01T00:00:00",\n "price": 44.86,\n "symbol": "AAPL"\n },\n {\n "date": "2005-03-01T00:00:00",\n "price": 41.67,\n "symbol": "AAPL"\n },\n {\n "date": "2005-04-01T00:00:00",\n "price": 36.06,\n "symbol": "AAPL"\n },\n {\n "date": "2005-05-01T00:00:00",\n "price": 39.76,\n "symbol": "AAPL"\n },\n {\n "date": "2005-06-01T00:00:00",\n "price": 36.81,\n "symbol": "AAPL"\n },\n {\n "date": "2005-07-01T00:00:00",\n "price": 42.65,\n "symbol": "AAPL"\n },\n {\n "date": "2005-08-01T00:00:00",\n "price": 46.89,\n "symbol": "AAPL"\n },\n {\n "date": "2005-09-01T00:00:00",\n "price": 53.61,\n "symbol": "AAPL"\n },\n {\n "date": "2005-10-01T00:00:00",\n "price": 57.59,\n "symbol": "AAPL"\n },\n {\n "date": "2005-11-01T00:00:00",\n "price": 67.82,\n "symbol": "AAPL"\n },\n {\n "date": "2005-12-01T00:00:00",\n "price": 71.89,\n "symbol": "AAPL"\n },\n {\n "date": "2006-01-01T00:00:00",\n "price": 75.51,\n "symbol": "AAPL"\n },\n {\n "date": "2006-02-01T00:00:00",\n "price": 68.49,\n "symbol": "AAPL"\n },\n {\n "date": "2006-03-01T00:00:00",\n "price": 62.72,\n "symbol": "AAPL"\n },\n {\n "date": "2006-04-01T00:00:00",\n "price": 70.39,\n "symbol": "AAPL"\n },\n {\n "date": "2006-05-01T00:00:00",\n "price": 59.77,\n "symbol": "AAPL"\n },\n {\n "date": "2006-06-01T00:00:00",\n "price": 57.27,\n "symbol": "AAPL"\n },\n {\n "date": "2006-07-01T00:00:00",\n "price": 67.96,\n "symbol": "AAPL"\n },\n {\n "date": "2006-08-01T00:00:00",\n "price": 67.85,\n "symbol": "AAPL"\n },\n {\n "date": "2006-09-01T00:00:00",\n "price": 76.98,\n "symbol": "AAPL"\n },\n {\n "date": "2006-10-01T00:00:00",\n "price": 81.08,\n "symbol": "AAPL"\n },\n {\n "date": "2006-11-01T00:00:00",\n "price": 91.66,\n "symbol": "AAPL"\n },\n {\n "date": "2006-12-01T00:00:00",\n "price": 84.84,\n "symbol": "AAPL"\n },\n {\n "date": "2007-01-01T00:00:00",\n "price": 85.73,\n "symbol": "AAPL"\n },\n {\n "date": "2007-02-01T00:00:00",\n "price": 84.61,\n "symbol": "AAPL"\n },\n {\n "date": "2007-03-01T00:00:00",\n "price": 92.91,\n "symbol": "AAPL"\n },\n {\n "date": "2007-04-01T00:00:00",\n "price": 99.8,\n "symbol": "AAPL"\n },\n {\n "date": "2007-05-01T00:00:00",\n "price": 121.19,\n "symbol": "AAPL"\n },\n {\n "date": "2007-06-01T00:00:00",\n "price": 122.04,\n "symbol": "AAPL"\n },\n {\n "date": "2007-07-01T00:00:00",\n "price": 131.76,\n "symbol": "AAPL"\n },\n {\n "date": "2007-08-01T00:00:00",\n "price": 138.48,\n "symbol": "AAPL"\n },\n {\n "date": "2007-09-01T00:00:00",\n "price": 153.47,\n "symbol": "AAPL"\n },\n {\n "date": "2007-10-01T00:00:00",\n "price": 189.95,\n "symbol": "AAPL"\n },\n {\n "date": "2007-11-01T00:00:00",\n "price": 182.22,\n "symbol": "AAPL"\n },\n {\n "date": "2007-12-01T00:00:00",\n "price": 198.08,\n "symbol": "AAPL"\n },\n {\n "date": "2008-01-01T00:00:00",\n "price": 135.36,\n "symbol": "AAPL"\n },\n {\n "date": "2008-02-01T00:00:00",\n "price": 125.02,\n "symbol": "AAPL"\n },\n {\n "date": "2008-03-01T00:00:00",\n "price": 143.5,\n "symbol": "AAPL"\n },\n {\n "date": "2008-04-01T00:00:00",\n "price": 173.95,\n "symbol": "AAPL"\n },\n {\n "date": "2008-05-01T00:00:00",\n "price": 188.75,\n "symbol": "AAPL"\n },\n {\n "date": "2008-06-01T00:00:00",\n "price": 167.44,\n "symbol": "AAPL"\n },\n {\n "date": "2008-07-01T00:00:00",\n "price": 158.95,\n "symbol": "AAPL"\n },\n {\n "date": "2008-08-01T00:00:00",\n "price": 169.53,\n "symbol": "AAPL"\n },\n {\n "date": "2008-09-01T00:00:00",\n "price": 113.66,\n "symbol": "AAPL"\n },\n {\n "date": "2008-10-01T00:00:00",\n "price": 107.59,\n "symbol": "AAPL"\n },\n {\n "date": "2008-11-01T00:00:00",\n "price": 92.67,\n "symbol": "AAPL"\n },\n {\n "date": "2008-12-01T00:00:00",\n "price": 85.35,\n "symbol": "AAPL"\n },\n {\n "date": "2009-01-01T00:00:00",\n "price": 90.13,\n "symbol": "AAPL"\n },\n {\n "date": "2009-02-01T00:00:00",\n "price": 89.31,\n "symbol": "AAPL"\n },\n {\n "date": "2009-03-01T00:00:00",\n "price": 105.12,\n "symbol": "AAPL"\n },\n {\n "date": "2009-04-01T00:00:00",\n "price": 125.83,\n "symbol": "AAPL"\n },\n {\n "date": "2009-05-01T00:00:00",\n "price": 135.81,\n "symbol": "AAPL"\n },\n {\n "date": "2009-06-01T00:00:00",\n "price": 142.43,\n "symbol": "AAPL"\n },\n {\n "date": "2009-07-01T00:00:00",\n "price": 163.39,\n "symbol": "AAPL"\n },\n {\n "date": "2009-08-01T00:00:00",\n "price": 168.21,\n "symbol": "AAPL"\n },\n {\n "date": "2009-09-01T00:00:00",\n "price": 185.35,\n "symbol": "AAPL"\n },\n {\n "date": "2009-10-01T00:00:00",\n "price": 188.5,\n "symbol": "AAPL"\n },\n {\n "date": "2009-11-01T00:00:00",\n "price": 199.91,\n "symbol": "AAPL"\n },\n {\n "date": "2009-12-01T00:00:00",\n "price": 210.73,\n "symbol": "AAPL"\n },\n {\n "date": "2010-01-01T00:00:00",\n "price": 192.06,\n "symbol": "AAPL"\n },\n {\n "date": "2010-02-01T00:00:00",\n "price": 204.62,\n "symbol": "AAPL"\n },\n {\n "date": "2010-03-01T00:00:00",\n "price": 223.02,\n "symbol": "AAPL"\n }\n ]\n },\n "layer": [\n {\n "encoding": {\n "color": {\n "field": "symbol",\n "legend": {\n "orient": "bottom",\n "title": null\n },\n "scale": {\n "domain": [\n "MSFT",\n "AMZN",\n "IBM",\n "GOOG",\n "AAPL"\n ],\n "range": [\n 0,\n 1,\n 2,\n 3,\n 4\n ]\n },\n "type": "nominal"\n },\n "size": {\n "condition": {\n "param": "param_10",\n "value": 4\n },\n "value": 1\n },\n "strokeDash": {},\n "tooltip": [\n {\n "field": "date",\n "type": "temporal"\n },\n {\n "field": "price",\n "type": "quantitative"\n },\n {\n "field": "symbol",\n "type": "nominal"\n }\n ],\n "x": {\n "axis": {},\n "field": "date",\n "title": "date",\n "type": "temporal"\n },\n "y": {\n "axis": {},\n "field": "price",\n "title": "price",\n "type": "quantitative"\n }\n },\n "mark": {\n "clip": true,\n "type": "line"\n },\n "name": "view_5"\n },\n {\n "encoding": {\n "color": {\n "field": "symbol",\n "legend": {\n "orient": "bottom",\n "title": null\n },\n "scale": {\n "domain": [\n "MSFT",\n "AMZN",\n "IBM",\n "GOOG",\n "AAPL"\n ],\n "range": [\n 0,\n 1,\n 2,\n 3,\n 4\n ]\n },\n "type": "nominal"\n },\n "opacity": {\n "value": 0\n },\n "tooltip": [\n {\n "field": "date",\n "type": "temporal"\n },\n {\n "field": "price",\n "type": "quantitative"\n },\n {\n "field": "symbol",\n "type": "nominal"\n }\n ],\n "x": {\n "axis": {},\n "field": "date",\n "title": "date",\n "type": "temporal"\n },\n "y": {\n "axis": {},\n "field": "price",\n "title": "price",\n "type": "quantitative"\n }\n },\n "mark": {\n "clip": true,\n "type": "point"\n },\n "name": "view_4"\n }\n ],\n "params": [\n {\n "name": "param_10",\n "select": {\n "fields": [\n "symbol"\n ],\n "nearest": true,\n "on": "mouseover",\n "type": "point"\n },\n "views": [\n "view_4"\n ]\n },\n {\n "bind": "scales",\n "name": "param_11",\n "select": {\n "encodings": [\n "x",\n "y"\n ],\n "type": "interval"\n },\n "views": [\n "view_5"\n ]\n }\n ],\n "title": "Stock Prices"\n}',
	chart: "line"
};

export let scatter_plot = {
	type: "altair",
	plot: '{\n "$schema": "https://vega.github.io/schema/vega-lite/v5.17.0.json",\n "background": "transparent",\n "config": {\n "view": {\n "continuousHeight": 300,\n "continuousWidth": 300\n }\n },\n "data": {\n "name": "data-583e73726c1545c56c203344161a975c"\n },\n "datasets": {\n "data-583e73726c1545c56c203344161a975c": [\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 307.0,\n "Horsepower": 130.0,\n "Miles_per_Gallon": 18.0,\n "Name": "chevrolet chevelle malibu",\n "Origin": "USA",\n "Weight_in_lbs": 3504,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 165.0,\n "Miles_per_Gallon": 15.0,\n "Name": "buick skylark 320",\n "Origin": "USA",\n "Weight_in_lbs": 3693,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 18.0,\n "Name": "plymouth satellite",\n "Origin": "USA",\n "Weight_in_lbs": 3436,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 16.0,\n "Name": "amc rebel sst",\n "Origin": "USA",\n "Weight_in_lbs": 3433,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 10.5,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 17.0,\n "Name": "ford torino",\n "Origin": "USA",\n "Weight_in_lbs": 3449,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 10.0,\n "Cylinders": 8,\n "Displacement": 429.0,\n "Horsepower": 198.0,\n "Miles_per_Gallon": 15.0,\n "Name": "ford galaxie 500",\n "Origin": "USA",\n "Weight_in_lbs": 4341,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 9.0,\n "Cylinders": 8,\n "Displacement": 454.0,\n "Horsepower": 220.0,\n "Miles_per_Gallon": 14.0,\n "Name": "chevrolet impala",\n "Origin": "USA",\n "Weight_in_lbs": 4354,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 8.5,\n "Cylinders": 8,\n "Displacement": 440.0,\n "Horsepower": 215.0,\n "Miles_per_Gallon": 14.0,\n "Name": "plymouth fury iii",\n "Origin": "USA",\n "Weight_in_lbs": 4312,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 10.0,\n "Cylinders": 8,\n "Displacement": 455.0,\n "Horsepower": 225.0,\n "Miles_per_Gallon": 14.0,\n "Name": "pontiac catalina",\n "Origin": "USA",\n "Weight_in_lbs": 4425,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 8.5,\n "Cylinders": 8,\n "Displacement": 390.0,\n "Horsepower": 190.0,\n "Miles_per_Gallon": 15.0,\n "Name": "amc ambassador dpl",\n "Origin": "USA",\n "Weight_in_lbs": 3850,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 17.5,\n "Cylinders": 4,\n "Displacement": 133.0,\n "Horsepower": 115.0,\n "Miles_per_Gallon": null,\n "Name": "citroen ds-21 pallas",\n "Origin": "Europe",\n "Weight_in_lbs": 3090,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 165.0,\n "Miles_per_Gallon": null,\n "Name": "chevrolet chevelle concours (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4142,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 153.0,\n "Miles_per_Gallon": null,\n "Name": "ford torino (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4034,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 10.5,\n "Cylinders": 8,\n "Displacement": 383.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": null,\n "Name": "plymouth satellite (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4166,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 360.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": null,\n "Name": "amc rebel sst (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 3850,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 10.0,\n "Cylinders": 8,\n "Displacement": 383.0,\n "Horsepower": 170.0,\n "Miles_per_Gallon": 15.0,\n "Name": "dodge challenger se",\n "Origin": "USA",\n "Weight_in_lbs": 3563,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 8.0,\n "Cylinders": 8,\n "Displacement": 340.0,\n "Horsepower": 160.0,\n "Miles_per_Gallon": 14.0,\n "Name": "plymouth \'cuda 340",\n "Origin": "USA",\n "Weight_in_lbs": 3609,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 8.0,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": null,\n "Name": "ford mustang boss 302",\n "Origin": "USA",\n "Weight_in_lbs": 3353,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 9.5,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 15.0,\n "Name": "chevrolet monte carlo",\n "Origin": "USA",\n "Weight_in_lbs": 3761,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 10.0,\n "Cylinders": 8,\n "Displacement": 455.0,\n "Horsepower": 225.0,\n "Miles_per_Gallon": 14.0,\n "Name": "buick estate wagon (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 3086,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 113.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 24.0,\n "Name": "toyota corona mark ii",\n "Origin": "Japan",\n "Weight_in_lbs": 2372,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 198.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 22.0,\n "Name": "plymouth duster",\n "Origin": "USA",\n "Weight_in_lbs": 2833,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 199.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 18.0,\n "Name": "amc hornet",\n "Origin": "USA",\n "Weight_in_lbs": 2774,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 21.0,\n "Name": "ford maverick",\n "Origin": "USA",\n "Weight_in_lbs": 2587,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 27.0,\n "Name": "datsun pl510",\n "Origin": "Japan",\n "Weight_in_lbs": 2130,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 20.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 46.0,\n "Miles_per_Gallon": 26.0,\n "Name": "volkswagen 1131 deluxe sedan",\n "Origin": "Europe",\n "Weight_in_lbs": 1835,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 17.5,\n "Cylinders": 4,\n "Displacement": 110.0,\n "Horsepower": 87.0,\n "Miles_per_Gallon": 25.0,\n "Name": "peugeot 504",\n "Origin": "Europe",\n "Weight_in_lbs": 2672,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 107.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 24.0,\n "Name": "audi 100 ls",\n "Origin": "Europe",\n "Weight_in_lbs": 2430,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 17.5,\n "Cylinders": 4,\n "Displacement": 104.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 25.0,\n "Name": "saab 99e",\n "Origin": "Europe",\n "Weight_in_lbs": 2375,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 113.0,\n "Miles_per_Gallon": 26.0,\n "Name": "bmw 2002",\n "Origin": "Europe",\n "Weight_in_lbs": 2234,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 6,\n "Displacement": 199.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 21.0,\n "Name": "amc gremlin",\n "Origin": "USA",\n "Weight_in_lbs": 2648,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 360.0,\n "Horsepower": 215.0,\n "Miles_per_Gallon": 10.0,\n "Name": "ford f250",\n "Origin": "USA",\n "Weight_in_lbs": 4615,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 8,\n "Displacement": 307.0,\n "Horsepower": 200.0,\n "Miles_per_Gallon": 10.0,\n "Name": "chevy c20",\n "Origin": "USA",\n "Weight_in_lbs": 4376,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 210.0,\n "Miles_per_Gallon": 11.0,\n "Name": "dodge d200",\n "Origin": "USA",\n "Weight_in_lbs": 4382,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 18.5,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 193.0,\n "Miles_per_Gallon": 9.0,\n "Name": "hi 1200d",\n "Origin": "USA",\n "Weight_in_lbs": 4732,\n "Year": "1970-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 27.0,\n "Name": "datsun pl510",\n "Origin": "Japan",\n "Weight_in_lbs": 2130,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 28.0,\n "Name": "chevrolet vega 2300",\n "Origin": "USA",\n "Weight_in_lbs": 2264,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 113.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 25.0,\n "Name": "toyota corona",\n "Origin": "Japan",\n "Weight_in_lbs": 2228,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": null,\n "Miles_per_Gallon": 25.0,\n "Name": "ford pinto",\n "Origin": "USA",\n "Weight_in_lbs": 2046,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 20.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 48.0,\n "Miles_per_Gallon": null,\n "Name": "volkswagen super beetle 117",\n "Origin": "Europe",\n "Weight_in_lbs": 1978,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 19.0,\n "Name": "amc gremlin",\n "Origin": "USA",\n "Weight_in_lbs": 2634,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 16.0,\n "Name": "plymouth satellite custom",\n "Origin": "USA",\n "Weight_in_lbs": 3439,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 17.0,\n "Name": "chevrolet chevelle malibu",\n "Origin": "USA",\n "Weight_in_lbs": 3329,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 19.0,\n "Name": "ford torino 500",\n "Origin": "USA",\n "Weight_in_lbs": 3302,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 18.0,\n "Name": "amc matador",\n "Origin": "USA",\n "Weight_in_lbs": 3288,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 165.0,\n "Miles_per_Gallon": 14.0,\n "Name": "chevrolet impala",\n "Origin": "USA",\n "Weight_in_lbs": 4209,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": 14.0,\n "Name": "pontiac catalina brougham",\n "Origin": "USA",\n "Weight_in_lbs": 4464,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 153.0,\n "Miles_per_Gallon": 14.0,\n "Name": "ford galaxie 500",\n "Origin": "USA",\n "Weight_in_lbs": 4154,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 14.0,\n "Name": "plymouth fury iii",\n "Origin": "USA",\n "Weight_in_lbs": 4096,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 383.0,\n "Horsepower": 180.0,\n "Miles_per_Gallon": 12.0,\n "Name": "dodge monaco (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4955,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 170.0,\n "Miles_per_Gallon": 13.0,\n "Name": "ford country squire (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4746,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": 13.0,\n "Name": "pontiac safari (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 5140,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 6,\n "Displacement": 258.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 18.0,\n "Name": "amc hornet sportabout (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 2962,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 72.0,\n "Miles_per_Gallon": 22.0,\n "Name": "chevrolet vega (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 2408,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 19.0,\n "Name": "pontiac firebird",\n "Origin": "USA",\n "Weight_in_lbs": 3282,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 18.0,\n "Name": "ford mustang",\n "Origin": "USA",\n "Weight_in_lbs": 3139,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 86.0,\n "Miles_per_Gallon": 23.0,\n "Name": "mercury capri 2000",\n "Origin": "USA",\n "Weight_in_lbs": 2220,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 116.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 28.0,\n "Name": "opel 1900",\n "Origin": "Europe",\n "Weight_in_lbs": 2123,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 19.5,\n "Cylinders": 4,\n "Displacement": 79.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 30.0,\n "Name": "peugeot 304",\n "Origin": "Europe",\n "Weight_in_lbs": 2074,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 88.0,\n "Horsepower": 76.0,\n "Miles_per_Gallon": 30.0,\n "Name": "fiat 124b",\n "Origin": "Europe",\n "Weight_in_lbs": 2065,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 71.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 31.0,\n "Name": "toyota corolla 1200",\n "Origin": "Japan",\n "Weight_in_lbs": 1773,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 4,\n "Displacement": 72.0,\n "Horsepower": 69.0,\n "Miles_per_Gallon": 35.0,\n "Name": "datsun 1200",\n "Origin": "Japan",\n "Weight_in_lbs": 1613,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 60.0,\n "Miles_per_Gallon": 27.0,\n "Name": "volkswagen model 111",\n "Origin": "Europe",\n "Weight_in_lbs": 1834,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 20.5,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 26.0,\n "Name": "plymouth cricket",\n "Origin": "USA",\n "Weight_in_lbs": 1955,\n "Year": "1971-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 113.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 24.0,\n "Name": "toyota corona hardtop",\n "Origin": "Japan",\n "Weight_in_lbs": 2278,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 97.5,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 25.0,\n "Name": "dodge colt hardtop",\n "Origin": "USA",\n "Weight_in_lbs": 2126,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 23.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 54.0,\n "Miles_per_Gallon": 23.0,\n "Name": "volkswagen type 3",\n "Origin": "Europe",\n "Weight_in_lbs": 2254,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 19.5,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 20.0,\n "Name": "chevrolet vega",\n "Origin": "USA",\n "Weight_in_lbs": 2408,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 86.0,\n "Miles_per_Gallon": 21.0,\n "Name": "ford pinto runabout",\n "Origin": "USA",\n "Weight_in_lbs": 2226,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 165.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chevrolet impala",\n "Origin": "USA",\n "Weight_in_lbs": 4274,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": 14.0,\n "Name": "pontiac catalina",\n "Origin": "USA",\n "Weight_in_lbs": 4385,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 15.0,\n "Name": "plymouth fury iii",\n "Origin": "USA",\n "Weight_in_lbs": 4135,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 153.0,\n "Miles_per_Gallon": 14.0,\n "Name": "ford galaxie 500",\n "Origin": "USA",\n "Weight_in_lbs": 4129,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 17.0,\n "Name": "amc ambassador sst",\n "Origin": "USA",\n "Weight_in_lbs": 3672,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 429.0,\n "Horsepower": 208.0,\n "Miles_per_Gallon": 11.0,\n "Name": "mercury marquis",\n "Origin": "USA",\n "Weight_in_lbs": 4633,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 155.0,\n "Miles_per_Gallon": 13.0,\n "Name": "buick lesabre custom",\n "Origin": "USA",\n "Weight_in_lbs": 4502,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 160.0,\n "Miles_per_Gallon": 12.0,\n "Name": "oldsmobile delta 88 royale",\n "Origin": "USA",\n "Weight_in_lbs": 4456,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 190.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chrysler newport royal",\n "Origin": "USA",\n "Weight_in_lbs": 4422,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 3,\n "Displacement": 70.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 19.0,\n "Name": "mazda rx2 coupe",\n "Origin": "Japan",\n "Weight_in_lbs": 2330,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 15.0,\n "Name": "amc matador (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 3892,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 307.0,\n "Horsepower": 130.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chevrolet chevelle concours (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4098,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 13.0,\n "Name": "ford gran torino (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4294,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 14.0,\n "Name": "plymouth satellite custom (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4077,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 112.0,\n "Miles_per_Gallon": 18.0,\n "Name": "volvo 145e (sw)",\n "Origin": "Europe",\n "Weight_in_lbs": 2933,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 76.0,\n "Miles_per_Gallon": 22.0,\n "Name": "volkswagen 411 (sw)",\n "Origin": "Europe",\n "Weight_in_lbs": 2511,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 19.5,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 87.0,\n "Miles_per_Gallon": 21.0,\n "Name": "peugeot 504 (sw)",\n "Origin": "Europe",\n "Weight_in_lbs": 2979,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 4,\n "Displacement": 96.0,\n "Horsepower": 69.0,\n "Miles_per_Gallon": 26.0,\n "Name": "renault 12 (sw)",\n "Origin": "Europe",\n "Weight_in_lbs": 2189,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 86.0,\n "Miles_per_Gallon": 22.0,\n "Name": "ford pinto (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 2395,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 92.0,\n "Miles_per_Gallon": 28.0,\n "Name": "datsun 510 (sw)",\n "Origin": "Japan",\n "Weight_in_lbs": 2288,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 23.0,\n "Name": "toyouta corona mark ii (sw)",\n "Origin": "Japan",\n "Weight_in_lbs": 2506,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 28.0,\n "Name": "dodge colt (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 2164,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 27.0,\n "Name": "toyota corolla 1600 (sw)",\n "Origin": "Japan",\n "Weight_in_lbs": 2100,\n "Year": "1972-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": 13.0,\n "Name": "buick century 350",\n "Origin": "USA",\n "Weight_in_lbs": 4100,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 14.0,\n "Name": "amc matador",\n "Origin": "USA",\n "Weight_in_lbs": 3672,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chevrolet malibu",\n "Origin": "USA",\n "Weight_in_lbs": 3988,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 137.0,\n "Miles_per_Gallon": 14.0,\n "Name": "ford gran torino",\n "Origin": "USA",\n "Weight_in_lbs": 4042,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 15.0,\n "Name": "dodge coronet custom",\n "Origin": "USA",\n "Weight_in_lbs": 3777,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 429.0,\n "Horsepower": 198.0,\n "Miles_per_Gallon": 12.0,\n "Name": "mercury marquis brougham",\n "Origin": "USA",\n "Weight_in_lbs": 4952,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chevrolet caprice classic",\n "Origin": "USA",\n "Weight_in_lbs": 4464,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 158.0,\n "Miles_per_Gallon": 13.0,\n "Name": "ford ltd",\n "Origin": "USA",\n "Weight_in_lbs": 4363,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 14.0,\n "Name": "plymouth fury gran sedan",\n "Origin": "USA",\n "Weight_in_lbs": 4237,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 440.0,\n "Horsepower": 215.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chrysler new yorker brougham",\n "Origin": "USA",\n "Weight_in_lbs": 4735,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 455.0,\n "Horsepower": 225.0,\n "Miles_per_Gallon": 12.0,\n "Name": "buick electra 225 custom",\n "Origin": "USA",\n "Weight_in_lbs": 4951,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 360.0,\n "Horsepower": 175.0,\n "Miles_per_Gallon": 13.0,\n "Name": "amc ambassador brougham",\n "Origin": "USA",\n "Weight_in_lbs": 3821,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 18.0,\n "Name": "plymouth valiant",\n "Origin": "USA",\n "Weight_in_lbs": 3121,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 16.0,\n "Name": "chevrolet nova custom",\n "Origin": "USA",\n "Weight_in_lbs": 3278,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 18.0,\n "Name": "amc hornet",\n "Origin": "USA",\n "Weight_in_lbs": 2945,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 18.0,\n "Name": "ford maverick",\n "Origin": "USA",\n "Weight_in_lbs": 3021,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 198.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 23.0,\n "Name": "plymouth duster",\n "Origin": "USA",\n "Weight_in_lbs": 2904,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 21.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 46.0,\n "Miles_per_Gallon": 26.0,\n "Name": "volkswagen super beetle",\n "Origin": "Europe",\n "Weight_in_lbs": 1950,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 11.0,\n "Name": "chevrolet impala",\n "Origin": "USA",\n "Weight_in_lbs": 4997,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 167.0,\n "Miles_per_Gallon": 12.0,\n "Name": "ford country",\n "Origin": "USA",\n "Weight_in_lbs": 4906,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 360.0,\n "Horsepower": 170.0,\n "Miles_per_Gallon": 13.0,\n "Name": "plymouth custom suburb",\n "Origin": "USA",\n "Weight_in_lbs": 4654,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 180.0,\n "Miles_per_Gallon": 12.0,\n "Name": "oldsmobile vista cruiser",\n "Origin": "USA",\n "Weight_in_lbs": 4499,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 18.0,\n "Name": "amc gremlin",\n "Origin": "USA",\n "Weight_in_lbs": 2789,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 20.0,\n "Name": "toyota carina",\n "Origin": "Japan",\n "Weight_in_lbs": 2279,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 19.5,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 72.0,\n "Miles_per_Gallon": 21.0,\n "Name": "chevrolet vega",\n "Origin": "USA",\n "Weight_in_lbs": 2401,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 108.0,\n "Horsepower": 94.0,\n "Miles_per_Gallon": 22.0,\n "Name": "datsun 610",\n "Origin": "Japan",\n "Weight_in_lbs": 2379,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 3,\n "Displacement": 70.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 18.0,\n "Name": "maxda rx3",\n "Origin": "Japan",\n "Weight_in_lbs": 2124,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 18.5,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 19.0,\n "Name": "ford pinto",\n "Origin": "USA",\n "Weight_in_lbs": 2310,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 6,\n "Displacement": 155.0,\n "Horsepower": 107.0,\n "Miles_per_Gallon": 21.0,\n "Name": "mercury capri v6",\n "Origin": "USA",\n "Weight_in_lbs": 2472,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 26.0,\n "Name": "fiat 124 sport coupe",\n "Origin": "Europe",\n "Weight_in_lbs": 2265,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 15.0,\n "Name": "chevrolet monte carlo s",\n "Origin": "USA",\n "Weight_in_lbs": 4082,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 9.5,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 230.0,\n "Miles_per_Gallon": 16.0,\n "Name": "pontiac grand prix",\n "Origin": "USA",\n "Weight_in_lbs": 4278,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 19.5,\n "Cylinders": 4,\n "Displacement": 68.0,\n "Horsepower": 49.0,\n "Miles_per_Gallon": 29.0,\n "Name": "fiat 128",\n "Origin": "Europe",\n "Weight_in_lbs": 1867,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 116.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 24.0,\n "Name": "opel manta",\n "Origin": "Europe",\n "Weight_in_lbs": 2158,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 114.0,\n "Horsepower": 91.0,\n "Miles_per_Gallon": 20.0,\n "Name": "audi 100ls",\n "Origin": "Europe",\n "Weight_in_lbs": 2582,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 112.0,\n "Miles_per_Gallon": 19.0,\n "Name": "volvo 144ea",\n "Origin": "Europe",\n "Weight_in_lbs": 2868,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 15.0,\n "Name": "dodge dart custom",\n "Origin": "USA",\n "Weight_in_lbs": 3399,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 24.0,\n "Name": "saab 99le",\n "Origin": "Europe",\n "Weight_in_lbs": 2660,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 6,\n "Displacement": 156.0,\n "Horsepower": 122.0,\n "Miles_per_Gallon": 20.0,\n "Name": "toyota mark ii",\n "Origin": "Japan",\n "Weight_in_lbs": 2807,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 11.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 180.0,\n "Miles_per_Gallon": 11.0,\n "Name": "oldsmobile omega",\n "Origin": "USA",\n "Weight_in_lbs": 3664,\n "Year": "1973-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 6,\n "Displacement": 198.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 20.0,\n "Name": "plymouth duster",\n "Origin": "USA",\n "Weight_in_lbs": 3102,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": null,\n "Miles_per_Gallon": 21.0,\n "Name": "ford maverick",\n "Origin": "USA",\n "Weight_in_lbs": 2875,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 19.0,\n "Name": "amc hornet",\n "Origin": "USA",\n "Weight_in_lbs": 2901,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 15.0,\n "Name": "chevrolet nova",\n "Origin": "USA",\n "Weight_in_lbs": 3336,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 79.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 31.0,\n "Name": "datsun b210",\n "Origin": "Japan",\n "Weight_in_lbs": 1950,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 26.0,\n "Name": "ford pinto",\n "Origin": "USA",\n "Weight_in_lbs": 2451,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 21.0,\n "Cylinders": 4,\n "Displacement": 71.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 32.0,\n "Name": "toyota corolla 1200",\n "Origin": "Japan",\n "Weight_in_lbs": 1836,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 25.0,\n "Name": "chevrolet vega",\n "Origin": "USA",\n "Weight_in_lbs": 2542,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 16.0,\n "Name": "chevrolet chevelle malibu classic",\n "Origin": "USA",\n "Weight_in_lbs": 3781,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 6,\n "Displacement": 258.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 16.0,\n "Name": "amc matador",\n "Origin": "USA",\n "Weight_in_lbs": 3632,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 18.0,\n "Name": "plymouth satellite sebring",\n "Origin": "USA",\n "Weight_in_lbs": 3613,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 16.0,\n "Name": "ford gran torino",\n "Origin": "USA",\n "Weight_in_lbs": 4141,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 13.0,\n "Name": "buick century luxus (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4699,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 14.0,\n "Name": "dodge coronet custom (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4457,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 14.0,\n "Name": "ford gran torino (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4638,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 14.0,\n "Name": "amc matador (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4257,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 83.0,\n "Miles_per_Gallon": 29.0,\n "Name": "audi fox",\n "Origin": "Europe",\n "Weight_in_lbs": 2219,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 79.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 26.0,\n "Name": "volkswagen dasher",\n "Origin": "Europe",\n "Weight_in_lbs": 1963,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 78.0,\n "Miles_per_Gallon": 26.0,\n "Name": "opel manta",\n "Origin": "Europe",\n "Weight_in_lbs": 2300,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 76.0,\n "Horsepower": 52.0,\n "Miles_per_Gallon": 31.0,\n "Name": "toyota corona",\n "Origin": "Japan",\n "Weight_in_lbs": 1649,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 4,\n "Displacement": 83.0,\n "Horsepower": 61.0,\n "Miles_per_Gallon": 32.0,\n "Name": "datsun 710",\n "Origin": "Japan",\n "Weight_in_lbs": 2003,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 28.0,\n "Name": "dodge colt",\n "Origin": "USA",\n "Weight_in_lbs": 2125,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 24.0,\n "Name": "fiat 128",\n "Origin": "Europe",\n "Weight_in_lbs": 2108,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 116.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 26.0,\n "Name": "fiat 124 tc",\n "Origin": "Europe",\n "Weight_in_lbs": 2246,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 24.0,\n "Name": "honda civic",\n "Origin": "Japan",\n "Weight_in_lbs": 2489,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 108.0,\n "Horsepower": 93.0,\n "Miles_per_Gallon": 26.0,\n "Name": "subaru",\n "Origin": "Japan",\n "Weight_in_lbs": 2391,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 79.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 31.0,\n "Name": "fiat x1.9",\n "Origin": "Europe",\n "Weight_in_lbs": 2000,\n "Year": "1974-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 19.0,\n "Name": "plymouth valiant custom",\n "Origin": "USA",\n "Weight_in_lbs": 3264,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 18.0,\n "Name": "chevrolet nova",\n "Origin": "USA",\n "Weight_in_lbs": 3459,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 21.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 72.0,\n "Miles_per_Gallon": 15.0,\n "Name": "mercury monarch",\n "Origin": "USA",\n "Weight_in_lbs": 3432,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 19.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 72.0,\n "Miles_per_Gallon": 15.0,\n "Name": "ford maverick",\n "Origin": "USA",\n "Weight_in_lbs": 3158,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 11.5,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 170.0,\n "Miles_per_Gallon": 16.0,\n "Name": "pontiac catalina",\n "Origin": "USA",\n "Weight_in_lbs": 4668,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 15.0,\n "Name": "chevrolet bel air",\n "Origin": "USA",\n "Weight_in_lbs": 4440,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 16.0,\n "Name": "plymouth grand fury",\n "Origin": "USA",\n "Weight_in_lbs": 4498,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 148.0,\n "Miles_per_Gallon": 14.0,\n "Name": "ford ltd",\n "Origin": "USA",\n "Weight_in_lbs": 4657,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 21.0,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 17.0,\n "Name": "buick century",\n "Origin": "USA",\n "Weight_in_lbs": 3907,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 18.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 16.0,\n "Name": "chevroelt chevelle malibu",\n "Origin": "USA",\n "Weight_in_lbs": 3897,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 6,\n "Displacement": 258.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 15.0,\n "Name": "amc matador",\n "Origin": "USA",\n "Weight_in_lbs": 3730,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 18.0,\n "Name": "plymouth fury",\n "Origin": "USA",\n "Weight_in_lbs": 3785,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 21.0,\n "Name": "buick skyhawk",\n "Origin": "USA",\n "Weight_in_lbs": 3039,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 8,\n "Displacement": 262.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 20.0,\n "Name": "chevrolet monza 2+2",\n "Origin": "USA",\n "Weight_in_lbs": 3221,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 129.0,\n "Miles_per_Gallon": 13.0,\n "Name": "ford mustang ii",\n "Origin": "USA",\n "Weight_in_lbs": 3169,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 29.0,\n "Name": "toyota corolla",\n "Origin": "Japan",\n "Weight_in_lbs": 2171,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 83.0,\n "Miles_per_Gallon": 23.0,\n "Name": "ford pinto",\n "Origin": "USA",\n "Weight_in_lbs": 2639,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 20.0,\n "Name": "amc gremlin",\n "Origin": "USA",\n "Weight_in_lbs": 2914,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 18.5,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 78.0,\n "Miles_per_Gallon": 23.0,\n "Name": "pontiac astro",\n "Origin": "USA",\n "Weight_in_lbs": 2592,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 4,\n "Displacement": 134.0,\n "Horsepower": 96.0,\n "Miles_per_Gallon": 24.0,\n "Name": "toyota corona",\n "Origin": "Japan",\n "Weight_in_lbs": 2702,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 71.0,\n "Miles_per_Gallon": 25.0,\n "Name": "volkswagen dasher",\n "Origin": "Europe",\n "Weight_in_lbs": 2223,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 119.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 24.0,\n "Name": "datsun 710",\n "Origin": "Japan",\n "Weight_in_lbs": 2545,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 6,\n "Displacement": 171.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 18.0,\n "Name": "ford pinto",\n "Origin": "USA",\n "Weight_in_lbs": 2984,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 29.0,\n "Name": "volkswagen rabbit",\n "Origin": "Europe",\n "Weight_in_lbs": 1937,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 19.0,\n "Name": "amc pacer",\n "Origin": "USA",\n "Weight_in_lbs": 3211,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 115.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 23.0,\n "Name": "audi 100ls",\n "Origin": "Europe",\n "Weight_in_lbs": 2694,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 23.0,\n "Name": "peugeot 504",\n "Origin": "Europe",\n "Weight_in_lbs": 2957,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 98.0,\n "Miles_per_Gallon": 22.0,\n "Name": "volvo 244dl",\n "Origin": "Europe",\n "Weight_in_lbs": 2945,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 115.0,\n "Miles_per_Gallon": 25.0,\n "Name": "saab 99le",\n "Origin": "Europe",\n "Weight_in_lbs": 2671,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 17.5,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 53.0,\n "Miles_per_Gallon": 33.0,\n "Name": "honda civic cvcc",\n "Origin": "Japan",\n "Weight_in_lbs": 1795,\n "Year": "1975-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 107.0,\n "Horsepower": 86.0,\n "Miles_per_Gallon": 28.0,\n "Name": "fiat 131",\n "Origin": "Europe",\n "Weight_in_lbs": 2464,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 16.9,\n "Cylinders": 4,\n "Displacement": 116.0,\n "Horsepower": 81.0,\n "Miles_per_Gallon": 25.0,\n "Name": "opel 1900",\n "Origin": "Europe",\n "Weight_in_lbs": 2220,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 92.0,\n "Miles_per_Gallon": 25.0,\n "Name": "capri ii",\n "Origin": "USA",\n "Weight_in_lbs": 2572,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.7,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 79.0,\n "Miles_per_Gallon": 26.0,\n "Name": "dodge colt",\n "Origin": "USA",\n "Weight_in_lbs": 2255,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 15.3,\n "Cylinders": 4,\n "Displacement": 101.0,\n "Horsepower": 83.0,\n "Miles_per_Gallon": 27.0,\n "Name": "renault 12tl",\n "Origin": "Europe",\n "Weight_in_lbs": 2202,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 305.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 17.5,\n "Name": "chevrolet chevelle malibu classic",\n "Origin": "USA",\n "Weight_in_lbs": 4215,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 16.0,\n "Name": "dodge coronet brougham",\n "Origin": "USA",\n "Weight_in_lbs": 4190,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 13.9,\n "Cylinders": 8,\n "Displacement": 304.0,\n "Horsepower": 120.0,\n "Miles_per_Gallon": 15.5,\n "Name": "amc matador",\n "Origin": "USA",\n "Weight_in_lbs": 3962,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 12.8,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 152.0,\n "Miles_per_Gallon": 14.5,\n "Name": "ford gran torino",\n "Origin": "USA",\n "Weight_in_lbs": 4215,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 15.4,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 22.0,\n "Name": "plymouth valiant",\n "Origin": "USA",\n "Weight_in_lbs": 3233,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 22.0,\n "Name": "chevrolet nova",\n "Origin": "USA",\n "Weight_in_lbs": 3353,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.6,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 81.0,\n "Miles_per_Gallon": 24.0,\n "Name": "ford maverick",\n "Origin": "USA",\n "Weight_in_lbs": 3012,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.6,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 22.5,\n "Name": "amc hornet",\n "Origin": "USA",\n "Weight_in_lbs": 3085,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 22.2,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 52.0,\n "Miles_per_Gallon": 29.0,\n "Name": "chevrolet chevette",\n "Origin": "USA",\n "Weight_in_lbs": 2035,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 22.1,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 60.0,\n "Miles_per_Gallon": 24.5,\n "Name": "chevrolet woody",\n "Origin": "USA",\n "Weight_in_lbs": 2164,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 14.2,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 29.0,\n "Name": "vw rabbit",\n "Origin": "Europe",\n "Weight_in_lbs": 1937,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.4,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 53.0,\n "Miles_per_Gallon": 33.0,\n "Name": "honda civic",\n "Origin": "Japan",\n "Weight_in_lbs": 1795,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.7,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 20.0,\n "Name": "dodge aspen se",\n "Origin": "USA",\n "Weight_in_lbs": 3651,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 21.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 78.0,\n "Miles_per_Gallon": 18.0,\n "Name": "ford granada ghia",\n "Origin": "USA",\n "Weight_in_lbs": 3574,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 16.2,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 18.5,\n "Name": "pontiac ventura sj",\n "Origin": "USA",\n "Weight_in_lbs": 3645,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.8,\n "Cylinders": 6,\n "Displacement": 258.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 17.5,\n "Name": "amc pacer d/l",\n "Origin": "USA",\n "Weight_in_lbs": 3193,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 12.2,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 71.0,\n "Miles_per_Gallon": 29.5,\n "Name": "volkswagen rabbit",\n "Origin": "Europe",\n "Weight_in_lbs": 1825,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 32.0,\n "Name": "datsun b-210",\n "Origin": "Japan",\n "Weight_in_lbs": 1990,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 28.0,\n "Name": "toyota corolla",\n "Origin": "Japan",\n "Weight_in_lbs": 2155,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 13.6,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 72.0,\n "Miles_per_Gallon": 26.5,\n "Name": "ford pinto",\n "Origin": "USA",\n "Weight_in_lbs": 2565,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 15.7,\n "Cylinders": 4,\n "Displacement": 130.0,\n "Horsepower": 102.0,\n "Miles_per_Gallon": 20.0,\n "Name": "volvo 245",\n "Origin": "Europe",\n "Weight_in_lbs": 3150,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 13.2,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 13.0,\n "Name": "plymouth volare premier v8",\n "Origin": "USA",\n "Weight_in_lbs": 3940,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 21.9,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 19.0,\n "Name": "peugeot 504",\n "Origin": "Europe",\n "Weight_in_lbs": 3270,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 6,\n "Displacement": 156.0,\n "Horsepower": 108.0,\n "Miles_per_Gallon": 19.0,\n "Name": "toyota mark ii",\n "Origin": "Japan",\n "Weight_in_lbs": 2930,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 16.7,\n "Cylinders": 6,\n "Displacement": 168.0,\n "Horsepower": 120.0,\n "Miles_per_Gallon": 16.5,\n "Name": "mercedes-benz 280s",\n "Origin": "Europe",\n "Weight_in_lbs": 3820,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 12.1,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 180.0,\n "Miles_per_Gallon": 16.5,\n "Name": "cadillac seville",\n "Origin": "USA",\n "Weight_in_lbs": 4380,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 12.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 13.0,\n "Name": "chevy c10",\n "Origin": "USA",\n "Weight_in_lbs": 4055,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 130.0,\n "Miles_per_Gallon": 13.0,\n "Name": "ford f108",\n "Origin": "USA",\n "Weight_in_lbs": 3870,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 13.0,\n "Name": "dodge d100",\n "Origin": "USA",\n "Weight_in_lbs": 3755,\n "Year": "1976-01-01T00:00:00"\n },\n {\n "Acceleration": 18.5,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 68.0,\n "Miles_per_Gallon": 31.5,\n "Name": "honda Accelerationord cvcc",\n "Origin": "Japan",\n "Weight_in_lbs": 2045,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 14.8,\n "Cylinders": 4,\n "Displacement": 111.0,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 30.0,\n "Name": "buick opel isuzu deluxe",\n "Origin": "USA",\n "Weight_in_lbs": 2155,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 18.6,\n "Cylinders": 4,\n "Displacement": 79.0,\n "Horsepower": 58.0,\n "Miles_per_Gallon": 36.0,\n "Name": "renault 5 gtl",\n "Origin": "Europe",\n "Weight_in_lbs": 1825,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 96.0,\n "Miles_per_Gallon": 25.5,\n "Name": "plymouth arrow gs",\n "Origin": "USA",\n "Weight_in_lbs": 2300,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 16.8,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 33.5,\n "Name": "datsun f-10 hatchback",\n "Origin": "Japan",\n "Weight_in_lbs": 1945,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 8,\n "Displacement": 305.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 17.5,\n "Name": "chevrolet caprice classic",\n "Origin": "USA",\n "Weight_in_lbs": 3880,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 8,\n "Displacement": 260.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 17.0,\n "Name": "oldsmobile cutlass supreme",\n "Origin": "USA",\n "Weight_in_lbs": 4060,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 13.7,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 15.5,\n "Name": "dodge monaco brougham",\n "Origin": "USA",\n "Weight_in_lbs": 4140,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 130.0,\n "Miles_per_Gallon": 15.0,\n "Name": "mercury cougar brougham",\n "Origin": "USA",\n "Weight_in_lbs": 4295,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 17.5,\n "Name": "chevrolet concours",\n "Origin": "USA",\n "Weight_in_lbs": 3520,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 16.9,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 20.5,\n "Name": "buick skylark",\n "Origin": "USA",\n "Weight_in_lbs": 3425,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 17.7,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 19.0,\n "Name": "plymouth volare custom",\n "Origin": "USA",\n "Weight_in_lbs": 3630,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 6,\n "Displacement": 250.0,\n "Horsepower": 98.0,\n "Miles_per_Gallon": 18.5,\n "Name": "ford granada",\n "Origin": "USA",\n "Weight_in_lbs": 3525,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 11.1,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 180.0,\n "Miles_per_Gallon": 16.0,\n "Name": "pontiac grand prix lj",\n "Origin": "USA",\n "Weight_in_lbs": 4220,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 11.4,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 170.0,\n "Miles_per_Gallon": 15.5,\n "Name": "chevrolet monte carlo landau",\n "Origin": "USA",\n "Weight_in_lbs": 4165,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 12.2,\n "Cylinders": 8,\n "Displacement": 400.0,\n "Horsepower": 190.0,\n "Miles_per_Gallon": 15.5,\n "Name": "chrysler cordoba",\n "Origin": "USA",\n "Weight_in_lbs": 4325,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 149.0,\n "Miles_per_Gallon": 16.0,\n "Name": "ford thunderbird",\n "Origin": "USA",\n "Weight_in_lbs": 4335,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 78.0,\n "Miles_per_Gallon": 29.0,\n "Name": "volkswagen rabbit custom",\n "Origin": "Europe",\n "Weight_in_lbs": 1940,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 24.5,\n "Name": "pontiac sunbird coupe",\n "Origin": "USA",\n "Weight_in_lbs": 2740,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 18.2,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 26.0,\n "Name": "toyota corolla liftback",\n "Origin": "Japan",\n "Weight_in_lbs": 2265,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 89.0,\n "Miles_per_Gallon": 25.5,\n "Name": "ford mustang ii 2+2",\n "Origin": "USA",\n "Weight_in_lbs": 2755,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 63.0,\n "Miles_per_Gallon": 30.5,\n "Name": "chevrolet chevette",\n "Origin": "USA",\n "Weight_in_lbs": 2051,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 15.9,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 83.0,\n "Miles_per_Gallon": 33.5,\n "Name": "dodge colt m/m",\n "Origin": "USA",\n "Weight_in_lbs": 2075,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 30.0,\n "Name": "subaru dl",\n "Origin": "Japan",\n "Weight_in_lbs": 1985,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 14.1,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 78.0,\n "Miles_per_Gallon": 30.5,\n "Name": "volkswagen dasher",\n "Origin": "Europe",\n "Weight_in_lbs": 2190,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 6,\n "Displacement": 146.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 22.0,\n "Name": "datsun 810",\n "Origin": "Japan",\n "Weight_in_lbs": 2815,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 12.8,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 21.5,\n "Name": "bmw 320i",\n "Origin": "Europe",\n "Weight_in_lbs": 2600,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 13.5,\n "Cylinders": 3,\n "Displacement": 80.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 21.5,\n "Name": "mazda rx-4",\n "Origin": "Japan",\n "Weight_in_lbs": 2720,\n "Year": "1977-01-01T00:00:00"\n },\n {\n "Acceleration": 21.5,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 48.0,\n "Miles_per_Gallon": 43.1,\n "Name": "volkswagen rabbit custom diesel",\n "Origin": "Europe",\n "Weight_in_lbs": 1985,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.4,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 66.0,\n "Miles_per_Gallon": 36.1,\n "Name": "ford fiesta",\n "Origin": "USA",\n "Weight_in_lbs": 1800,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 19.4,\n "Cylinders": 4,\n "Displacement": 78.0,\n "Horsepower": 52.0,\n "Miles_per_Gallon": 32.8,\n "Name": "mazda glc deluxe",\n "Origin": "Japan",\n "Weight_in_lbs": 1985,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 18.6,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 39.4,\n "Name": "datsun b210 gx",\n "Origin": "Japan",\n "Weight_in_lbs": 2070,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 60.0,\n "Miles_per_Gallon": 36.1,\n "Name": "honda civic cvcc",\n "Origin": "Japan",\n "Weight_in_lbs": 1800,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 8,\n "Displacement": 260.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 19.9,\n "Name": "oldsmobile cutlass salon brougham",\n "Origin": "USA",\n "Weight_in_lbs": 3365,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 13.2,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 19.4,\n "Name": "dodge diplomat",\n "Origin": "USA",\n "Weight_in_lbs": 3735,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 12.8,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 139.0,\n "Miles_per_Gallon": 20.2,\n "Name": "mercury monarch ghia",\n "Origin": "USA",\n "Weight_in_lbs": 3570,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 19.2,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 19.2,\n "Name": "pontiac phoenix lj",\n "Origin": "USA",\n "Weight_in_lbs": 3535,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 18.2,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 20.5,\n "Name": "chevrolet malibu",\n "Origin": "USA",\n "Weight_in_lbs": 3155,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 20.2,\n "Name": "ford fairmont (auto)",\n "Origin": "USA",\n "Weight_in_lbs": 2965,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.4,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 25.1,\n "Name": "ford fairmont (man)",\n "Origin": "USA",\n "Weight_in_lbs": 2720,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 17.2,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 20.5,\n "Name": "plymouth volare",\n "Origin": "USA",\n "Weight_in_lbs": 3430,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 17.2,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 19.4,\n "Name": "amc concord",\n "Origin": "USA",\n "Weight_in_lbs": 3210,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 20.6,\n "Name": "buick century special",\n "Origin": "USA",\n "Weight_in_lbs": 3380,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 16.7,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 20.8,\n "Name": "mercury zephyr",\n "Origin": "USA",\n "Weight_in_lbs": 3070,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 18.7,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 18.6,\n "Name": "dodge aspen",\n "Origin": "USA",\n "Weight_in_lbs": 3620,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.1,\n "Cylinders": 6,\n "Displacement": 258.0,\n "Horsepower": 120.0,\n "Miles_per_Gallon": 18.1,\n "Name": "amc concord d/l",\n "Origin": "USA",\n "Weight_in_lbs": 3410,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 13.2,\n "Cylinders": 8,\n "Displacement": 305.0,\n "Horsepower": 145.0,\n "Miles_per_Gallon": 19.2,\n "Name": "chevrolet monte carlo landau",\n "Origin": "USA",\n "Weight_in_lbs": 3425,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 13.4,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 165.0,\n "Miles_per_Gallon": 17.7,\n "Name": "buick regal sport coupe (turbo)",\n "Origin": "USA",\n "Weight_in_lbs": 3445,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 11.2,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 139.0,\n "Miles_per_Gallon": 18.1,\n "Name": "ford futura",\n "Origin": "USA",\n "Weight_in_lbs": 3205,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 13.7,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 140.0,\n "Miles_per_Gallon": 17.5,\n "Name": "dodge magnum xe",\n "Origin": "USA",\n "Weight_in_lbs": 4080,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 68.0,\n "Miles_per_Gallon": 30.0,\n "Name": "chevrolet chevette",\n "Origin": "USA",\n "Weight_in_lbs": 2155,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.2,\n "Cylinders": 4,\n "Displacement": 134.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 27.5,\n "Name": "toyota corona",\n "Origin": "Japan",\n "Weight_in_lbs": 2560,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.7,\n "Cylinders": 4,\n "Displacement": 119.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 27.2,\n "Name": "datsun 510",\n "Origin": "Japan",\n "Weight_in_lbs": 2300,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 30.9,\n "Name": "dodge omni",\n "Origin": "USA",\n "Weight_in_lbs": 2230,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.8,\n "Cylinders": 4,\n "Displacement": 134.0,\n "Horsepower": 95.0,\n "Miles_per_Gallon": 21.1,\n "Name": "toyota celica gt liftback",\n "Origin": "Japan",\n "Weight_in_lbs": 2515,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 16.7,\n "Cylinders": 4,\n "Displacement": 156.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 23.2,\n "Name": "plymouth sapporo",\n "Origin": "USA",\n "Weight_in_lbs": 2745,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 17.6,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 23.8,\n "Name": "oldsmobile starfire sx",\n "Origin": "USA",\n "Weight_in_lbs": 2855,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 4,\n "Displacement": 119.0,\n "Horsepower": 97.0,\n "Miles_per_Gallon": 23.9,\n "Name": "datsun 200-sx",\n "Origin": "Japan",\n "Weight_in_lbs": 2405,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.9,\n "Cylinders": 5,\n "Displacement": 131.0,\n "Horsepower": 103.0,\n "Miles_per_Gallon": 20.3,\n "Name": "audi 5000",\n "Origin": "Europe",\n "Weight_in_lbs": 2830,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 13.6,\n "Cylinders": 6,\n "Displacement": 163.0,\n "Horsepower": 125.0,\n "Miles_per_Gallon": 17.0,\n "Name": "volvo 264gl",\n "Origin": "Europe",\n "Weight_in_lbs": 3140,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.7,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 115.0,\n "Miles_per_Gallon": 21.6,\n "Name": "saab 99gle",\n "Origin": "Europe",\n "Weight_in_lbs": 2795,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 6,\n "Displacement": 163.0,\n "Horsepower": 133.0,\n "Miles_per_Gallon": 16.2,\n "Name": "peugeot 604sl",\n "Origin": "Europe",\n "Weight_in_lbs": 3410,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 4,\n "Displacement": 89.0,\n "Horsepower": 71.0,\n "Miles_per_Gallon": 31.5,\n "Name": "volkswagen scirocco",\n "Origin": "Europe",\n "Weight_in_lbs": 1990,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 16.6,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 68.0,\n "Miles_per_Gallon": 29.5,\n "Name": "honda Accelerationord lx",\n "Origin": "Japan",\n "Weight_in_lbs": 2135,\n "Year": "1978-01-01T00:00:00"\n },\n {\n "Acceleration": 15.4,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 115.0,\n "Miles_per_Gallon": 21.5,\n "Name": "pontiac lemans v6",\n "Origin": "USA",\n "Weight_in_lbs": 3245,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 18.2,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 19.8,\n "Name": "mercury zephyr 6",\n "Origin": "USA",\n "Weight_in_lbs": 2990,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 17.3,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 22.3,\n "Name": "ford fairmont 4",\n "Origin": "USA",\n "Weight_in_lbs": 2890,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 18.2,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 20.2,\n "Name": "amc concord dl 6",\n "Origin": "USA",\n "Weight_in_lbs": 3265,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 16.6,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 20.6,\n "Name": "dodge aspen 6",\n "Origin": "USA",\n "Weight_in_lbs": 3360,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 15.4,\n "Cylinders": 8,\n "Displacement": 305.0,\n "Horsepower": 130.0,\n "Miles_per_Gallon": 17.0,\n "Name": "chevrolet caprice classic",\n "Origin": "USA",\n "Weight_in_lbs": 3840,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 13.4,\n "Cylinders": 8,\n "Displacement": 302.0,\n "Horsepower": 129.0,\n "Miles_per_Gallon": 17.6,\n "Name": "ford ltd landau",\n "Origin": "USA",\n "Weight_in_lbs": 3725,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 13.2,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 138.0,\n "Miles_per_Gallon": 16.5,\n "Name": "mercury grand marquis",\n "Origin": "USA",\n "Weight_in_lbs": 3955,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 15.2,\n "Cylinders": 8,\n "Displacement": 318.0,\n "Horsepower": 135.0,\n "Miles_per_Gallon": 18.2,\n "Name": "dodge st. regis",\n "Origin": "USA",\n "Weight_in_lbs": 3830,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 155.0,\n "Miles_per_Gallon": 16.9,\n "Name": "buick estate wagon (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4360,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.3,\n "Cylinders": 8,\n "Displacement": 351.0,\n "Horsepower": 142.0,\n "Miles_per_Gallon": 15.5,\n "Name": "ford country squire (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 4054,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 8,\n "Displacement": 267.0,\n "Horsepower": 125.0,\n "Miles_per_Gallon": 19.2,\n "Name": "chevrolet malibu classic (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 3605,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 8,\n "Displacement": 360.0,\n "Horsepower": 150.0,\n "Miles_per_Gallon": 18.5,\n "Name": "chrysler lebaron town @ country (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 3940,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.0,\n "Cylinders": 4,\n "Displacement": 89.0,\n "Horsepower": 71.0,\n "Miles_per_Gallon": 31.9,\n "Name": "vw rabbit custom",\n "Origin": "Europe",\n "Weight_in_lbs": 1925,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 15.2,\n "Cylinders": 4,\n "Displacement": 86.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 34.1,\n "Name": "maxda glc deluxe",\n "Origin": "Japan",\n "Weight_in_lbs": 1975,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.4,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 35.7,\n "Name": "dodge colt hatchback custom",\n "Origin": "USA",\n "Weight_in_lbs": 1915,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 27.4,\n "Name": "amc spirit dl",\n "Origin": "USA",\n "Weight_in_lbs": 2670,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 20.1,\n "Cylinders": 5,\n "Displacement": 183.0,\n "Horsepower": 77.0,\n "Miles_per_Gallon": 25.4,\n "Name": "mercedes benz 300d",\n "Origin": "Europe",\n "Weight_in_lbs": 3530,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 17.4,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 125.0,\n "Miles_per_Gallon": 23.0,\n "Name": "cadillac eldorado",\n "Origin": "USA",\n "Weight_in_lbs": 3900,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 24.8,\n "Cylinders": 4,\n "Displacement": 141.0,\n "Horsepower": 71.0,\n "Miles_per_Gallon": 27.2,\n "Name": "peugeot 504",\n "Origin": "Europe",\n "Weight_in_lbs": 3190,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 22.2,\n "Cylinders": 8,\n "Displacement": 260.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 23.9,\n "Name": "oldsmobile cutlass salon brougham",\n "Origin": "USA",\n "Weight_in_lbs": 3420,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 13.2,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 34.2,\n "Name": "plymouth horizon",\n "Origin": "USA",\n "Weight_in_lbs": 2200,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 34.5,\n "Name": "plymouth horizon tc3",\n "Origin": "USA",\n "Weight_in_lbs": 2150,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 19.2,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 31.8,\n "Name": "datsun 210",\n "Origin": "Japan",\n "Weight_in_lbs": 2020,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.7,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 69.0,\n "Miles_per_Gallon": 37.3,\n "Name": "fiat strada custom",\n "Origin": "Europe",\n "Weight_in_lbs": 2130,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 28.4,\n "Name": "buick skylark limited",\n "Origin": "USA",\n "Weight_in_lbs": 2670,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 11.3,\n "Cylinders": 6,\n "Displacement": 173.0,\n "Horsepower": 115.0,\n "Miles_per_Gallon": 28.8,\n "Name": "chevrolet citation",\n "Origin": "USA",\n "Weight_in_lbs": 2595,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 12.9,\n "Cylinders": 6,\n "Displacement": 173.0,\n "Horsepower": 115.0,\n "Miles_per_Gallon": 26.8,\n "Name": "oldsmobile omega brougham",\n "Origin": "USA",\n "Weight_in_lbs": 2700,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 13.2,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 33.5,\n "Name": "pontiac phoenix",\n "Origin": "USA",\n "Weight_in_lbs": 2556,\n "Year": "1979-01-01T00:00:00"\n },\n {\n "Acceleration": 14.7,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 76.0,\n "Miles_per_Gallon": 41.5,\n "Name": "vw rabbit",\n "Origin": "Europe",\n "Weight_in_lbs": 2144,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 18.8,\n "Cylinders": 4,\n "Displacement": 89.0,\n "Horsepower": 60.0,\n "Miles_per_Gallon": 38.1,\n "Name": "toyota corolla tercel",\n "Origin": "Japan",\n "Weight_in_lbs": 1968,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 32.1,\n "Name": "chevrolet chevette",\n "Origin": "USA",\n "Weight_in_lbs": 2120,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 86.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 37.2,\n "Name": "datsun 310",\n "Origin": "Japan",\n "Weight_in_lbs": 2019,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 16.5,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 28.0,\n "Name": "chevrolet citation",\n "Origin": "USA",\n "Weight_in_lbs": 2678,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 18.1,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 26.4,\n "Name": "ford fairmont",\n "Origin": "USA",\n "Weight_in_lbs": 2870,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 20.1,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 24.3,\n "Name": "amc concord",\n "Origin": "USA",\n "Weight_in_lbs": 3003,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 18.7,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 19.1,\n "Name": "dodge aspen",\n "Origin": "USA",\n "Weight_in_lbs": 3381,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 78.0,\n "Miles_per_Gallon": 34.3,\n "Name": "audi 4000",\n "Origin": "Europe",\n "Weight_in_lbs": 2188,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.5,\n "Cylinders": 4,\n "Displacement": 134.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 29.8,\n "Name": "toyota corona liftback",\n "Origin": "Japan",\n "Weight_in_lbs": 2711,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 17.5,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 31.3,\n "Name": "mazda 626",\n "Origin": "Japan",\n "Weight_in_lbs": 2542,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 119.0,\n "Horsepower": 92.0,\n "Miles_per_Gallon": 37.0,\n "Name": "datsun 510 hatchback",\n "Origin": "Japan",\n "Weight_in_lbs": 2434,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.2,\n "Cylinders": 4,\n "Displacement": 108.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 32.2,\n "Name": "toyota corolla",\n "Origin": "Japan",\n "Weight_in_lbs": 2265,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 17.9,\n "Cylinders": 4,\n "Displacement": 86.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 46.6,\n "Name": "mazda glc",\n "Origin": "Japan",\n "Weight_in_lbs": 2110,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 14.4,\n "Cylinders": 4,\n "Displacement": 156.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 27.9,\n "Name": "dodge colt",\n "Origin": "USA",\n "Weight_in_lbs": 2800,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 19.2,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 40.8,\n "Name": "datsun 210",\n "Origin": "Japan",\n "Weight_in_lbs": 2110,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 21.7,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 48.0,\n "Miles_per_Gallon": 44.3,\n "Name": "vw rabbit c (diesel)",\n "Origin": "Europe",\n "Weight_in_lbs": 2085,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 23.7,\n "Cylinders": 4,\n "Displacement": 90.0,\n "Horsepower": 48.0,\n "Miles_per_Gallon": 43.4,\n "Name": "vw dasher (diesel)",\n "Origin": "Europe",\n "Weight_in_lbs": 2335,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 19.9,\n "Cylinders": 5,\n "Displacement": 121.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 36.4,\n "Name": "audi 5000s (diesel)",\n "Origin": "Europe",\n "Weight_in_lbs": 2950,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 21.8,\n "Cylinders": 4,\n "Displacement": 146.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 30.0,\n "Name": "mercedes-benz 240d",\n "Origin": "Europe",\n "Weight_in_lbs": 3250,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 13.8,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 44.6,\n "Name": "honda civic 1500 gl",\n "Origin": "Japan",\n "Weight_in_lbs": 1850,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 17.3,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": null,\n "Miles_per_Gallon": 40.9,\n "Name": "renault lecar deluxe",\n "Origin": "Europe",\n "Weight_in_lbs": 1835,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 33.8,\n "Name": "subaru dl",\n "Origin": "Japan",\n "Weight_in_lbs": 2145,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.3,\n "Cylinders": 4,\n "Displacement": 89.0,\n "Horsepower": 62.0,\n "Miles_per_Gallon": 29.8,\n "Name": "vokswagen rabbit",\n "Origin": "Europe",\n "Weight_in_lbs": 1845,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 11.4,\n "Cylinders": 6,\n "Displacement": 168.0,\n "Horsepower": 132.0,\n "Miles_per_Gallon": 32.7,\n "Name": "datsun 280-zx",\n "Origin": "Japan",\n "Weight_in_lbs": 2910,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 12.5,\n "Cylinders": 3,\n "Displacement": 70.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 23.7,\n "Name": "mazda rx-7 gs",\n "Origin": "Japan",\n "Weight_in_lbs": 2420,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.1,\n "Cylinders": 4,\n "Displacement": 122.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 35.0,\n "Name": "triumph tr7 coupe",\n "Origin": "Europe",\n "Weight_in_lbs": 2500,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 14.3,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": null,\n "Miles_per_Gallon": 23.6,\n "Name": "ford mustang cobra",\n "Origin": "USA",\n "Weight_in_lbs": 2905,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 4,\n "Displacement": 107.0,\n "Horsepower": 72.0,\n "Miles_per_Gallon": 32.4,\n "Name": "honda Accelerationord",\n "Origin": "Japan",\n "Weight_in_lbs": 2290,\n "Year": "1980-01-01T00:00:00"\n },\n {\n "Acceleration": 15.7,\n "Cylinders": 4,\n "Displacement": 135.0,\n "Horsepower": 84.0,\n "Miles_per_Gallon": 27.2,\n "Name": "plymouth reliant",\n "Origin": "USA",\n "Weight_in_lbs": 2490,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 84.0,\n "Miles_per_Gallon": 26.6,\n "Name": "buick skylark",\n "Origin": "USA",\n "Weight_in_lbs": 2635,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.4,\n "Cylinders": 4,\n "Displacement": 156.0,\n "Horsepower": 92.0,\n "Miles_per_Gallon": 25.8,\n "Name": "dodge aries wagon (sw)",\n "Origin": "USA",\n "Weight_in_lbs": 2620,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 12.6,\n "Cylinders": 6,\n "Displacement": 173.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 23.5,\n "Name": "chevrolet citation",\n "Origin": "USA",\n "Weight_in_lbs": 2725,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 12.9,\n "Cylinders": 4,\n "Displacement": 135.0,\n "Horsepower": 84.0,\n "Miles_per_Gallon": 30.0,\n "Name": "plymouth reliant",\n "Origin": "USA",\n "Weight_in_lbs": 2385,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.9,\n "Cylinders": 4,\n "Displacement": 79.0,\n "Horsepower": 58.0,\n "Miles_per_Gallon": 39.1,\n "Name": "toyota starlet",\n "Origin": "Japan",\n "Weight_in_lbs": 1755,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 86.0,\n "Horsepower": 64.0,\n "Miles_per_Gallon": 39.0,\n "Name": "plymouth champ",\n "Origin": "USA",\n "Weight_in_lbs": 1875,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.1,\n "Cylinders": 4,\n "Displacement": 81.0,\n "Horsepower": 60.0,\n "Miles_per_Gallon": 35.1,\n "Name": "honda civic 1300",\n "Origin": "Japan",\n "Weight_in_lbs": 1760,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.8,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 32.3,\n "Name": "subaru",\n "Origin": "Japan",\n "Weight_in_lbs": 2065,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 19.4,\n "Cylinders": 4,\n "Displacement": 85.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 37.0,\n "Name": "datsun 210",\n "Origin": "Japan",\n "Weight_in_lbs": 1975,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.3,\n "Cylinders": 4,\n "Displacement": 89.0,\n "Horsepower": 62.0,\n "Miles_per_Gallon": 37.7,\n "Name": "toyota tercel",\n "Origin": "Japan",\n "Weight_in_lbs": 2050,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 68.0,\n "Miles_per_Gallon": 34.1,\n "Name": "mazda glc 4",\n "Origin": "Japan",\n "Weight_in_lbs": 1985,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.9,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 63.0,\n "Miles_per_Gallon": 34.7,\n "Name": "plymouth horizon 4",\n "Origin": "USA",\n "Weight_in_lbs": 2215,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.2,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 34.4,\n "Name": "ford escort 4w",\n "Origin": "USA",\n "Weight_in_lbs": 2045,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 20.7,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 65.0,\n "Miles_per_Gallon": 29.9,\n "Name": "ford escort 2h",\n "Origin": "USA",\n "Weight_in_lbs": 2380,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.2,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 74.0,\n "Miles_per_Gallon": 33.0,\n "Name": "volkswagen jetta",\n "Origin": "Europe",\n "Weight_in_lbs": 2190,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 4,\n "Displacement": 100.0,\n "Horsepower": null,\n "Miles_per_Gallon": 34.5,\n "Name": "renault 18i",\n "Origin": "Europe",\n "Weight_in_lbs": 2320,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.4,\n "Cylinders": 4,\n "Displacement": 107.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 33.7,\n "Name": "honda prelude",\n "Origin": "Japan",\n "Weight_in_lbs": 2210,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.8,\n "Cylinders": 4,\n "Displacement": 108.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 32.4,\n "Name": "toyota corolla",\n "Origin": "Japan",\n "Weight_in_lbs": 2350,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.8,\n "Cylinders": 4,\n "Displacement": 119.0,\n "Horsepower": 100.0,\n "Miles_per_Gallon": 32.9,\n "Name": "datsun 200sx",\n "Origin": "Japan",\n "Weight_in_lbs": 2615,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 18.3,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 74.0,\n "Miles_per_Gallon": 31.6,\n "Name": "mazda 626",\n "Origin": "Japan",\n "Weight_in_lbs": 2635,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 20.4,\n "Cylinders": 4,\n "Displacement": 141.0,\n "Horsepower": 80.0,\n "Miles_per_Gallon": 28.1,\n "Name": "peugeot 505s turbo diesel",\n "Origin": "Europe",\n "Weight_in_lbs": 3230,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.4,\n "Cylinders": 4,\n "Displacement": 121.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": null,\n "Name": "saab 900s",\n "Origin": "Europe",\n "Weight_in_lbs": 2800,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 19.6,\n "Cylinders": 6,\n "Displacement": 145.0,\n "Horsepower": 76.0,\n "Miles_per_Gallon": 30.7,\n "Name": "volvo diesel",\n "Origin": "Europe",\n "Weight_in_lbs": 3160,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 12.6,\n "Cylinders": 6,\n "Displacement": 168.0,\n "Horsepower": 116.0,\n "Miles_per_Gallon": 25.4,\n "Name": "toyota cressida",\n "Origin": "Japan",\n "Weight_in_lbs": 2900,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 13.8,\n "Cylinders": 6,\n "Displacement": 146.0,\n "Horsepower": 120.0,\n "Miles_per_Gallon": 24.2,\n "Name": "datsun 810 maxima",\n "Origin": "Japan",\n "Weight_in_lbs": 2930,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.8,\n "Cylinders": 6,\n "Displacement": 231.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 22.4,\n "Name": "buick century",\n "Origin": "USA",\n "Weight_in_lbs": 3415,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 19.0,\n "Cylinders": 8,\n "Displacement": 350.0,\n "Horsepower": 105.0,\n "Miles_per_Gallon": 26.6,\n "Name": "oldsmobile cutlass ls",\n "Origin": "USA",\n "Weight_in_lbs": 3725,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.1,\n "Cylinders": 6,\n "Displacement": 200.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 20.2,\n "Name": "ford granada gl",\n "Origin": "USA",\n "Weight_in_lbs": 3060,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.6,\n "Cylinders": 6,\n "Displacement": 225.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 17.6,\n "Name": "chrysler lebaron salon",\n "Origin": "USA",\n "Weight_in_lbs": 3465,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 19.6,\n "Cylinders": 4,\n "Displacement": 112.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 28.0,\n "Name": "chevrolet cavalier",\n "Origin": "USA",\n "Weight_in_lbs": 2605,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 18.6,\n "Cylinders": 4,\n "Displacement": 112.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 27.0,\n "Name": "chevrolet cavalier wagon",\n "Origin": "USA",\n "Weight_in_lbs": 2640,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 4,\n "Displacement": 112.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 34.0,\n "Name": "chevrolet cavalier 2-door",\n "Origin": "USA",\n "Weight_in_lbs": 2395,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.2,\n "Cylinders": 4,\n "Displacement": 112.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 31.0,\n "Name": "pontiac j2000 se hatchback",\n "Origin": "USA",\n "Weight_in_lbs": 2575,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.0,\n "Cylinders": 4,\n "Displacement": 135.0,\n "Horsepower": 84.0,\n "Miles_per_Gallon": 29.0,\n "Name": "dodge aries se",\n "Origin": "USA",\n "Weight_in_lbs": 2525,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 18.0,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 27.0,\n "Name": "pontiac phoenix",\n "Origin": "USA",\n "Weight_in_lbs": 2735,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 92.0,\n "Miles_per_Gallon": 24.0,\n "Name": "ford fairmont futura",\n "Origin": "USA",\n "Weight_in_lbs": 2865,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 20.5,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": null,\n "Miles_per_Gallon": 23.0,\n "Name": "amc concord dl",\n "Origin": "USA",\n "Weight_in_lbs": 3035,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.3,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 74.0,\n "Miles_per_Gallon": 36.0,\n "Name": "volkswagen rabbit l",\n "Origin": "Europe",\n "Weight_in_lbs": 1980,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 18.2,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 68.0,\n "Miles_per_Gallon": 37.0,\n "Name": "mazda glc custom l",\n "Origin": "Japan",\n "Weight_in_lbs": 2025,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.6,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 68.0,\n "Miles_per_Gallon": 31.0,\n "Name": "mazda glc custom",\n "Origin": "Japan",\n "Weight_in_lbs": 1970,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.7,\n "Cylinders": 4,\n "Displacement": 105.0,\n "Horsepower": 63.0,\n "Miles_per_Gallon": 38.0,\n "Name": "plymouth horizon miser",\n "Origin": "USA",\n "Weight_in_lbs": 2125,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.3,\n "Cylinders": 4,\n "Displacement": 98.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 36.0,\n "Name": "mercury lynx l",\n "Origin": "USA",\n "Weight_in_lbs": 2125,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 88.0,\n "Miles_per_Gallon": 36.0,\n "Name": "nissan stanza xe",\n "Origin": "Japan",\n "Weight_in_lbs": 2160,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 107.0,\n "Horsepower": 75.0,\n "Miles_per_Gallon": 36.0,\n "Name": "honda Accelerationord",\n "Origin": "Japan",\n "Weight_in_lbs": 2205,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.9,\n "Cylinders": 4,\n "Displacement": 108.0,\n "Horsepower": 70.0,\n "Miles_per_Gallon": 34.0,\n "Name": "toyota corolla",\n "Origin": "Japan",\n "Weight_in_lbs": 2245,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.0,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 38.0,\n "Name": "honda civic",\n "Origin": "Japan",\n "Weight_in_lbs": 1965,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.7,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 32.0,\n "Name": "honda civic (auto)",\n "Origin": "Japan",\n "Weight_in_lbs": 1965,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.2,\n "Cylinders": 4,\n "Displacement": 91.0,\n "Horsepower": 67.0,\n "Miles_per_Gallon": 38.0,\n "Name": "datsun 310 gx",\n "Origin": "Japan",\n "Weight_in_lbs": 1995,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 16.4,\n "Cylinders": 6,\n "Displacement": 181.0,\n "Horsepower": 110.0,\n "Miles_per_Gallon": 25.0,\n "Name": "buick century limited",\n "Origin": "USA",\n "Weight_in_lbs": 2945,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.0,\n "Cylinders": 6,\n "Displacement": 262.0,\n "Horsepower": 85.0,\n "Miles_per_Gallon": 38.0,\n "Name": "oldsmobile cutlass ciera (diesel)",\n "Origin": "USA",\n "Weight_in_lbs": 3015,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.5,\n "Cylinders": 4,\n "Displacement": 156.0,\n "Horsepower": 92.0,\n "Miles_per_Gallon": 26.0,\n "Name": "chrysler lebaron medallion",\n "Origin": "USA",\n "Weight_in_lbs": 2585,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 14.7,\n "Cylinders": 6,\n "Displacement": 232.0,\n "Horsepower": 112.0,\n "Miles_per_Gallon": 22.0,\n "Name": "ford granada l",\n "Origin": "USA",\n "Weight_in_lbs": 2835,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 13.9,\n "Cylinders": 4,\n "Displacement": 144.0,\n "Horsepower": 96.0,\n "Miles_per_Gallon": 32.0,\n "Name": "toyota celica gt",\n "Origin": "Japan",\n "Weight_in_lbs": 2665,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 13.0,\n "Cylinders": 4,\n "Displacement": 135.0,\n "Horsepower": 84.0,\n "Miles_per_Gallon": 36.0,\n "Name": "dodge charger 2.2",\n "Origin": "USA",\n "Weight_in_lbs": 2370,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 17.3,\n "Cylinders": 4,\n "Displacement": 151.0,\n "Horsepower": 90.0,\n "Miles_per_Gallon": 27.0,\n "Name": "chevrolet camaro",\n "Origin": "USA",\n "Weight_in_lbs": 2950,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 15.6,\n "Cylinders": 4,\n "Displacement": 140.0,\n "Horsepower": 86.0,\n "Miles_per_Gallon": 27.0,\n "Name": "ford mustang gl",\n "Origin": "USA",\n "Weight_in_lbs": 2790,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 24.6,\n "Cylinders": 4,\n "Displacement": 97.0,\n "Horsepower": 52.0,\n "Miles_per_Gallon": 44.0,\n "Name": "vw pickup",\n "Origin": "Europe",\n "Weight_in_lbs": 2130,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 11.6,\n "Cylinders": 4,\n "Displacement": 135.0,\n "Horsepower": 84.0,\n "Miles_per_Gallon": 32.0,\n "Name": "dodge rampage",\n "Origin": "USA",\n "Weight_in_lbs": 2295,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 18.6,\n "Cylinders": 4,\n "Displacement": 120.0,\n "Horsepower": 79.0,\n "Miles_per_Gallon": 28.0,\n "Name": "ford ranger",\n "Origin": "USA",\n "Weight_in_lbs": 2625,\n "Year": "1982-01-01T00:00:00"\n },\n {\n "Acceleration": 19.4,\n "Cylinders": 4,\n "Displacement": 119.0,\n "Horsepower": 82.0,\n "Miles_per_Gallon": 31.0,\n "Name": "chevy s-10",\n "Origin": "USA",\n "Weight_in_lbs": 2720,\n "Year": "1982-01-01T00:00:00"\n }\n ]\n },\n "encoding": {\n "color": {\n "field": "Origin",\n "legend": {\n "orient": "bottom",\n "title": null\n },\n "scale": {\n "domain": [\n "USA",\n "Europe",\n "Japan"\n ],\n "range": [\n 0,\n 1,\n 2\n ]\n },\n "type": "nominal"\n },\n "tooltip": {\n "field": "Name",\n "type": "nominal"\n },\n "x": {\n "axis": {},\n "field": "Horsepower",\n "title": "Petal Width",\n "type": "quantitative"\n },\n "y": {\n "axis": {},\n "field": "Miles_per_Gallon",\n "title": "Miles per Gallon",\n "type": "quantitative"\n }\n },\n "mark": {\n "clip": true,\n "type": "point"\n },\n "params": [\n {\n "bind": "scales",\n "name": "param_6",\n "select": {\n "encodings": [\n "x",\n "y"\n ],\n "type": "interval"\n }\n }\n ],\n "title": "Car Data"\n}',
	chart: "scatter"
};
