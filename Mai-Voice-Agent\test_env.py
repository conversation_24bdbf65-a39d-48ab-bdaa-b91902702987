#!/usr/bin/env python3
"""
Test environment variable loading
"""

import os
from dotenv import load_dotenv

print("Current working directory:", os.getcwd())
print("Script directory:", os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
current_dir = os.path.dirname(os.path.abspath(__file__))
env_path = os.path.join(current_dir, '.env')
print(f"Looking for .env file at: {env_path}")
print(f".env file exists: {os.path.exists(env_path)}")

load_dotenv(env_path)

# Check key environment variables
print("\nEnvironment variables:")
print(f"GEMINI_API_KEY: {'SET' if os.getenv('GEMINI_API_KEY') else 'NOT SET'}")
print(f"EMAIL_ADDRESS: {os.getenv('EMAIL_ADDRESS', 'NOT SET')}")
print(f"EMAIL_PASSWORD: {'SET' if os.getenv('EMAIL_PASSWORD') else 'NOT SET'}")
print(f"SMTP_SERVER: {os.getenv('SMTP_SERVER', 'NOT SET')}")
print(f"SMTP_PORT: {os.getenv('SMTP_PORT', 'NOT SET')}")

# Test pydantic settings
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
    
    class TestSettings(BaseSettings):
        gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
        email_address: str = Field(..., env="EMAIL_ADDRESS")
        email_password: str = Field(..., env="EMAIL_PASSWORD")
        
        class Config:
            env_file = ".env"
    
    settings = TestSettings()
    print("\n✅ Pydantic settings loaded successfully!")
    print(f"API Key: {'SET' if settings.gemini_api_key else 'NOT SET'}")
    print(f"Email: {settings.email_address}")
    
except Exception as e:
    print(f"\n❌ Pydantic settings failed: {e}")
