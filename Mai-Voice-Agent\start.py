#!/usr/bin/env python3
"""
Mai Voice Agent Startup Script
Starts the Mai Voice Agent with proper configuration and error handling
"""

import os
import sys
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_environment():
    """Check if required environment variables are set"""
    required_vars = ['GEMINI_API_KEY', 'EMAIL_ADDRESS', 'EMAIL_PASSWORD']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 Please check your .env file or set these environment variables.")
        print("   See .env.example for reference.")
        return False
    
    return True

def main():
    """Main startup function"""
    print("🤖 Starting Mai Voice Agent for Critical Future...")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Import and run the main application
        from main import app, settings
        import uvicorn
        
        print(f"✅ Environment configured successfully")
        print(f"🌐 Starting server on port {settings.port}")
        print(f"🔗 Access Mai at: http://localhost:{settings.port}")
        print("=" * 50)
        
        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=settings.port,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
