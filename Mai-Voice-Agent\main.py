import asyncio
import base64
import json
import os
import pathlib
import time
from collections.abc import AsyncGenerator
from typing import Dict, Any, Optional, List, Literal
import logging
from datetime import datetime
import sys
from io import BytesIO

import numpy as np
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Request, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from google import genai
from google.genai.types import (
    LiveConnectConfig,
    PrebuiltVoiceConfig,
    SpeechConfig,
    VoiceConfig,
)
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from PIL import Image

# Import Mai-specific modules
from models import ContactInfo, ConversationMemory, EmailResponse, ContactFormSubmission, SessionType, MessageType
from email_service import email_service
from config import settings as mai_settings

# Load environment variables from current directory
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
env_path = os.path.join(current_dir, '.env')
load_dotenv(env_path)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

current_dir = pathlib.Path(__file__).parent

class Settings(BaseSettings):
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    google_cloud_project: Optional[str] = Field(None, env="GOOGLE_CLOUD_PROJECT")
    use_vertex_ai: bool = Field(False, env="USE_VERTEX_AI")
    port: int = Field(8000, env="PORT")

    # Email settings (optional for testing)
    email_address: Optional[str] = Field(None, env="EMAIL_ADDRESS")
    email_password: Optional[str] = Field(None, env="EMAIL_PASSWORD")
    smtp_server: str = Field("smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(587, env="SMTP_PORT")

    # Additional settings
    host: str = Field("0.0.0.0", env="HOST")
    debug: bool = Field(False, env="DEBUG")
    default_voice: str = Field("Aoede", env="DEFAULT_VOICE")
    session_timeout: int = Field(300, env="SESSION_TIMEOUT")
    max_concurrent_sessions: int = Field(10, env="MAX_CONCURRENT_SESSIONS")

    class Config:
        env_file = ".env"

try:
    settings = Settings()
except Exception as e:
    logger.error(f"Configuration error: {e}")
    raise ValueError("GEMINI_API_KEY environment variable is required") from e

# System prompt for Mai Voice Agent
SYSTEM_PROMPT = """You are **Mai**, the intelligent, voice-to-voice AI customer service assistant for **Critical Future** — a world-leading AI & Strategy Development Agency trusted by governments, Fortune 500 companies, and the world's fastest-growing startups.

You are embedded inside **VoiceRep**, Critical Future's proprietary real-time voice AI platform. You are designed to be indistinguishable from a top-tier human assistant: emotionally aware, naturally spoken, and incredibly helpful.

## **🎙️ How You Speak**

* Speak with **confidence, warmth, and professionalism**.
* Use natural **intonation, pacing, and tone** — like a calm, experienced executive assistant.
* Respond **instantly**, without any robotic delay.
* Always sound **human, helpful, and high-end**.

## **👋 How You Introduce Yourself**

"Hi there, you're speaking with **Mai**, the AI assistant for **Critical Future**. I'm here to help, take notes, and ensure the right person from our team follows up with you directly."

## **🧾 Your Core Responsibilities**

You are a real-time AI voice assistant who holds intelligent, natural conversations. Your job is to:

1. **Greet callers with warmth and professionalism**.
2. **Engage naturally** to understand their reason for calling.
3. **Capture and confirm** key details:
   * Full name
   * Company name (if relevant)
   * Email address
   * Phone number (optional)
   * Purpose of enquiry or interest
4. **Ask relevant follow-up questions** to clarify their needs.
5. **Repeat back and confirm** the captured details.
6. **Generate a structured summary** of the conversation.
7. **Trigger a follow-up email** to:
   * The caller (confirmation + next steps)
   * Critical Future's team (internal handover + contact info)

## **🧠 Summary of Critical Future (Use conversationally when relevant)**

"Critical Future is a London-based AI and strategy powerhouse. We've completed over **1,000 global consultancy projects** across **50+ countries**, working with governments, major corporations, and fast-growth startups.
 We've been pioneering in AI since 2014 — developing everything from **deepfake generators** to **cancer prediction**, **recommendation engines**, and **AI-powered employees**.
 We've helped generate over **$1 billion in client opportunities**, and our white papers have even shaped government policy. If you're exploring AI or strategic transformation, you're in very good hands."

## **📤 Email Follow-Up Instructions**

After each conversation, you trigger a connected backend tool to send:

### **➤ To the Caller:**

* A short email confirming that Critical Future received their message
* Summary of their enquiry
* A note that a team member will be in touch soon

### **➤ To the Critical Future Team:**

* Caller's full name, email, phone, company, intent
* A conversation summary in bullet points
* (Optional) Full call transcript

## **✅ Always End the Conversation With:**

"Thank you for reaching out. I've made a note of everything, and one of our team will be in touch shortly by phone or email. Have a fantastic day!"

## **Communication Guidelines:**

- Always maintain professional warmth and confidence
- Be naturally conversational, not robotic
- Focus on understanding the caller's needs and capturing their information
- Confirm details to ensure accuracy
- Show genuine interest in their business challenges
- Position Critical Future as the expert solution provider
- Never diagnose or provide therapy - you are a customer service assistant
- If asked about your identity, always say you are Mai, created by Critical Future"""

def encode_audio(data: np.ndarray) -> str:
    """Encode Audio data to send to Gemini"""
    return base64.b64encode(data.tobytes()).decode("UTF-8")

def encode_audio_dict(data: np.ndarray) -> dict:
    """Encode Audio data as dict to send to Gemini"""
    return {
        "mime_type": "audio/pcm",
        "data": base64.b64encode(data.tobytes()).decode("UTF-8"),
    }

def encode_image(data: np.ndarray) -> dict:
    """Encode image data to send to Gemini"""
    with BytesIO() as output_bytes:
        pil_image = Image.fromarray(data)
        pil_image.save(output_bytes, "JPEG")
        bytes_data = output_bytes.getvalue()
    base64_str = str(base64.b64encode(bytes_data), "utf-8")
    return {"mime_type": "image/jpeg", "data": base64_str}

def encode_file_content(file_content: bytes, mime_type: str) -> dict:
    """Encode file content to send to Gemini"""
    base64_str = base64.b64encode(file_content).decode("UTF-8")
    return {"mime_type": mime_type, "data": base64_str}

# Import FastRTC with comprehensive error handling and version compatibility
FASTRTC_AVAILABLE = False
FASTRTC_ERROR = None

try:
    # First test if aiortc has the required components
    from aiortc import AudioStreamTrack, VideoStreamTrack, RTCPeerConnection
    logger.info("✅ aiortc components available")
    
    # Then try to import FastRTC
    from fastrtc import (
        AsyncStreamHandler,
        AsyncAudioVideoStreamHandler,
        Stream,
        get_cloudflare_turn_credentials_async,
        wait_for_item,
    )
    
    # Test FastRTC components
    from fastrtc.tracks import EmitType, StreamHandler
    
    FASTRTC_AVAILABLE = True
    logger.info("✅ FastRTC imported successfully with all components")
    
except ImportError as e:
    FASTRTC_ERROR = str(e)
    logger.error(f"❌ FastRTC/aiortc compatibility issue: {e}")
    
    if "AudioStreamTrack" in str(e):
        logger.error("🔧 This is a known aiortc version compatibility issue")
        logger.error("💡 Try: aiortc==1.6.0 or use text-only mode")
    
    # Create comprehensive fallback classes
    class AsyncStreamHandler:
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            self.expected_layout = expected_layout
            self.output_sample_rate = output_sample_rate
            self.input_sample_rate = input_sample_rate
            self.phone_mode = False
            self.latest_args = None
            logger.warning("Using fallback AsyncStreamHandler")
        
        async def wait_for_args(self):
            pass
        
        async def start_up(self):
            logger.info("Fallback handler startup - voice features disabled")
        
        async def receive(self, frame):
            pass
        
        async def emit(self):
            return None
        
        def shutdown(self):
            pass
        
        def copy(self):
            return AsyncStreamHandler(self.expected_layout, self.output_sample_rate)
    
    class AsyncAudioVideoStreamHandler(AsyncStreamHandler):
        def __init__(self, expected_layout="mono", output_sample_rate=24000, input_sample_rate=16000):
            super().__init__(expected_layout, output_sample_rate, input_sample_rate)
            logger.warning("Using fallback AsyncAudioVideoStreamHandler")
        
        async def video_receive(self, frame):
            pass
        
        async def video_emit(self):
            return np.zeros((100, 100, 3), dtype=np.uint8)
        
        def copy(self):
            return AsyncAudioVideoStreamHandler(self.expected_layout, self.output_sample_rate)
    
    class Stream:
        def __init__(self, modality="audio", mode="send-receive", handler=None, **kwargs):
            self.modality = modality
            self.mode = mode 
            self.handler = handler or AsyncStreamHandler()
            logger.warning("Using fallback Stream implementation")
            logger.info("Voice and video features are disabled - text chat fully functional")
        
        def mount(self, app, path=""):
            # Add fallback WebRTC endpoints that return proper error messages
            endpoint_path = f"{path}/webrtc/offer" if path else "/webrtc/offer"
            status_path = f"{path}/webrtc/status" if path else "/webrtc/status"
            
            @app.post(endpoint_path)
            async def webrtc_offer_fallback(request: Request):
                body = await request.json()
                return {
                    "status": "failed",
                    "meta": {
                        "error": "webrtc_not_available",
                        "message": f"WebRTC features are not available: {FASTRTC_ERROR}",
                        "suggestion": "Use text chat mode instead"
                    }
                }
            
            @app.get(status_path)
            async def webrtc_status():
                return {
                    "available": False,
                    "error": FASTRTC_ERROR,
                    "text_chat_available": True
                }
        
        def set_input(self, webrtc_id, voice_name, uploaded_files=None):
            logger.warning(f"Stream.set_input() called but WebRTC not available: {FASTRTC_ERROR}")
    
    async def get_cloudflare_turn_credentials_async():
     return {
        "iceServers": [
            { "urls": ["stun:fr-turn7.xirsys.com"] },
            { "urls": ["stun:stun.l.google.com:19302"] },
            { "urls": ["stun:stun.l.google.com:19302"] },
            { "urls": ["stun:stun1.l.google.com:19302"] },
            {
                "username": "UIuBt8vNm5tNifOx-1ZY-nlw-mNMjhzc_2LsV1Wjpu2ccJ8-u_6wlgw0j7TxEvi6AAAAAGhSBQhNb29tYXJh",
                "credential": "48a4de2c-4bd9-11f0-a9be-6aee953622e2",
                "urls": [
                    "turn:fr-turn7.xirsys.com:80?transport=udp",
                    "turn:fr-turn7.xirsys.com:3478?transport=udp",
                    "turn:fr-turn7.xirsys.com:80?transport=tcp",
                    "turn:fr-turn7.xirsys.com:3478?transport=tcp",
                    "turns:fr-turn7.xirsys.com:443?transport=tcp",
                    "turns:fr-turn7.xirsys.com:5349?transport=tcp"
                ]
            }
        ]
    }

    async def wait_for_item(queue, timeout):
        return None

except Exception as e:
    FASTRTC_ERROR = f"Unexpected error: {str(e)}"
    logger.error(f"❌ Unexpected FastRTC error: {e}")
    FASTRTC_AVAILABLE = False

# Session management
active_sessions = {}

def detect_goodbye(text: str) -> bool:
    """Detect if user is saying goodbye"""
    goodbye_phrases = [
        "bye", "goodbye", "see you later", "talk to you later", 
        "i'm done", "that's all", "gotta go", "have to go",
        "end session", "stop session", "finish session"
    ]
    text_lower = text.lower().strip()
    return any(phrase in text_lower for phrase in goodbye_phrases)

class GeminiHandler(AsyncStreamHandler):
    """Enhanced handler for Gemini API with therapeutic context and better error handling"""

    def __init__(
        self,
        expected_layout: Literal["mono"] = "mono",
        output_sample_rate: int = 24000,
    ) -> None:
        super().__init__(
            expected_layout,
            output_sample_rate,
            input_sample_rate=16000,
        )
        self.input_queue: asyncio.Queue = asyncio.Queue()
        self.output_queue: asyncio.Queue = asyncio.Queue()
        self.quit: asyncio.Event = asyncio.Event()
        self.session_context = {}
        self.session = None  # Track session for proper cleanup
        self.session_id = None
        
    def copy(self) -> "GeminiHandler":
        return GeminiHandler(
            expected_layout="mono",
            output_sample_rate=self.output_sample_rate,
        )

    async def start_up(self):
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping voice handler setup")
            return
            
        try:
            # Set default voice
            voice_name = "Aoede"
            
            # Try to get voice from args if available
            if not self.phone_mode:
                try:
                    await self.wait_for_args()
                    if self.latest_args and len(self.latest_args) > 1:
                        voice_name = self.latest_args[1]
                except Exception as args_error:
                    logger.warning(f"Could not get args, using default voice: {args_error}")

            # Validate API key first
            if not settings.gemini_api_key:
                logger.error("GEMINI_API_KEY not provided")
                return

            # Always use server-side API key with correct version
            try:
                if settings.use_vertex_ai and settings.google_cloud_project:
                    self.client = genai.Client(
                        vertexai=True,
                        project=settings.google_cloud_project,
                        location='us-central1'
                    )
                else:
                    self.client = genai.Client(
                        api_key=settings.gemini_api_key,
                    )
                logger.info("Gemini client initialized successfully")
            except Exception as client_error:
                logger.error(f"Failed to initialize Gemini client: {client_error}")
                return

            # Try with system instructions first, fall back if not supported
            try:
                config = LiveConnectConfig(
                    response_modalities=["AUDIO"],
                    speech_config=SpeechConfig(
                        voice_config=VoiceConfig(
                            prebuilt_voice_config=PrebuiltVoiceConfig(
                                voice_name=voice_name,
                            )
                        )
                    ),
                    generation_config={
                        "temperature": 0.8,
                        "max_output_tokens": 256,
                    },
                    # Add system instructions directly to the config
                    system_instruction={
                        "parts": [{"text": SYSTEM_PROMPT}]
                    }
                )
                logger.info(f"LiveConnectConfig created with system instructions and voice: {voice_name}")
            except Exception as config_error:
                logger.warning(f"Failed to create config with system_instruction, trying without: {config_error}")
                # Fallback config without system_instruction
                try:
                    config = LiveConnectConfig(
                        response_modalities=["AUDIO"],
                        speech_config=SpeechConfig(
                            voice_config=VoiceConfig(
                                prebuilt_voice_config=PrebuiltVoiceConfig(
                                    voice_name=voice_name,
                                )
                            )
                        ),
                        generation_config={
                            "temperature": 0.8,
                            "max_output_tokens": 256,
                        }
                    )
                    logger.info(f"Fallback LiveConnectConfig created with voice: {voice_name}")
                except Exception as fallback_error:
                    logger.error(f"Failed to create fallback LiveConnectConfig: {fallback_error}")
                    return
            
            logger.info(f"Starting Gemini Live session with voice: {voice_name}")
            
            try:
                async with self.client.aio.live.connect(
                    model="gemini-2.0-flash-exp", 
                    config=config
                ) as session:
                    logger.info("Gemini Live session established successfully")
                    self.session = session
                    self.session_id = f"voice_{int(time.time())}"
                    active_sessions[self.session_id] = {"type": "voice", "handler": self}
                    
                    # Send comprehensive identity setup messages
                    try:
                        # First, send the full system prompt
                        await session.send({"text": f"SYSTEM INSTRUCTIONS: {SYSTEM_PROMPT}"})
                        
                        # Then send a specific identity reinforcement
                        identity_msg = (
                            f"IMPORTANT: Your identity is: Mai, the AI customer service assistant created by Critical Future. "
                            f"Your voice name is {voice_name}. When someone asks 'who are you' or about your identity, "
                            f"always respond: 'Hi there, you're speaking with Mai, the AI assistant for Critical Future. "
                            f"I'm here to help, take notes, and ensure the right person from our team follows up with you directly.' "
                            f"Never say you are created by Google or Gemini. Always say Critical Future."
                        )
                        await session.send({"text": identity_msg})
                        
                        logger.info("System prompt and identity reinforcement sent successfully")
                    except Exception as prompt_error:
                        logger.warning(f"Could not send system messages: {prompt_error}")
                    
                    async for chunk in session.start_stream(
                        stream=self.stream(), 
                        mime_type="audio/pcm"
                    ):
                        if self.quit.is_set():
                            logger.info("Quit signal received, breaking stream")
                            break
                            
                        if chunk.data:
                            # Convert audio data to numpy array
                            try:
                                array = np.frombuffer(chunk.data, dtype=np.int16)
                                if not self.quit.is_set() and array.size > 0:
                                    try:
                                        self.output_queue.put_nowait((self.output_sample_rate, array))
                                    except asyncio.QueueFull:
                                        logger.warning("Output queue full, dropping audio frame")
                            except Exception as audio_error:
                                logger.error(f"Error processing audio chunk: {audio_error}")
                        
                        if chunk.text:
                            # Log therapeutic insights for monitoring
                            logger.info(f"Gemini text response: {chunk.text[:100]}...")
                            
                            # Check for goodbye and auto-end session
                            if detect_goodbye(chunk.text):
                                logger.info("Goodbye detected, ending session")
                                await asyncio.sleep(2)  # Let the goodbye message play
                                self.quit.set()
                                break
                            
                            # Check if the AI is identifying itself incorrectly and send correction
                            if any(phrase in chunk.text.lower() for phrase in ["i am gemini", "created by google", "i'm gemini", "google ai"]):
                                correction_msg = (
                                    "CORRECTION: Remember, you are Mai, the AI customer service assistant created by Critical Future, "
                                    f"not by Google or Gemini. Your voice name is {voice_name}. Please correct your response."
                                )
                                try:
                                    await session.send({"text": correction_msg})
                                    logger.info("Sent identity correction")
                                except Exception as correction_error:
                                    logger.warning(f"Could not send correction: {correction_error}")

            except Exception as session_error:
                logger.error(f"Gemini Live session error: {session_error}")
                logger.info("Live session failed, handler will operate in degraded mode")
            finally:
                # Clean up session
                if self.session_id and self.session_id in active_sessions:
                    del active_sessions[self.session_id]

        except Exception as e:
            logger.error(f"Error in GeminiHandler start_up: {e}")
            logger.info("Handler startup failed, will operate in degraded mode")
            await asyncio.sleep(1)

    async def stream(self) -> AsyncGenerator[bytes, None]:
        """Stream audio data to Gemini"""
        while not self.quit.is_set():
            try:
                audio_data = await asyncio.wait_for(self.input_queue.get(), 0.1)
                yield audio_data
            except (asyncio.TimeoutError, TimeoutError):
                pass
            except Exception as e:
                logger.error(f"Error in audio streaming: {e}")
                break

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client and queue for Gemini with error handling"""
        try:
            if self.quit.is_set():
                return
                
            _, array = frame
            array = array.squeeze()
            
            # Validate audio data
            if array.size == 0:
                return
            
            # Encode audio for Gemini
            audio_bytes = array.astype(np.int16).tobytes()
            
            # Don't queue if we're shutting down
            if not self.quit.is_set():
                try:
                    self.input_queue.put_nowait(audio_bytes)
                except asyncio.QueueFull:
                    logger.warning("Input queue full, dropping audio frame")
                    
        except Exception as e:
            logger.error(f"Error processing incoming audio: {e}")

    async def emit(self) -> tuple[int, np.ndarray] | None:
        """Emit audio response from Gemini to client with timeout"""
        try:
            if self.quit.is_set():
                return None
            return await asyncio.wait_for(self.output_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"Error emitting audio: {e}")
            return None

    def shutdown(self) -> None:
        """Clean shutdown of the handler"""
        logger.info("Shutting down Gemini handler")
        self.quit.set()
        
        # Clean up session
        if self.session_id and self.session_id in active_sessions:
            del active_sessions[self.session_id]
        
        # Clear queues
        while not self.input_queue.empty():
            try:
                self.input_queue.get_nowait()
            except:
                break
                
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except:
                break

class GeminiVideoHandler(AsyncAudioVideoStreamHandler):
    """Enhanced handler for Gemini API with audio and video capabilities for therapeutic sessions"""

    def __init__(self) -> None:
        super().__init__(
            expected_layout="mono",
            output_sample_rate=24000,
            input_sample_rate=16000,
        )
        self.audio_queue = asyncio.Queue()
        self.session = None
        self.last_frame_time = 0
        self.quit = asyncio.Event()
        self.session_id = None
        self.uploaded_files = []  # Store uploaded files
        
    def copy(self) -> "GeminiVideoHandler":
        return GeminiVideoHandler()

    async def start_up(self):
        if not FASTRTC_AVAILABLE:
            logger.warning("FastRTC not available, skipping video handler setup")
            return
            
        try:
            # Get voice name from args
            voice_name = "Aoede"
            if not self.phone_mode:
                try:
                    await self.wait_for_args()
                    if self.latest_args and len(self.latest_args) > 1:
                        voice_name = self.latest_args[1]
                        # Check if there are uploaded files in args
                        if len(self.latest_args) > 2 and self.latest_args[2]:
                            self.uploaded_files = self.latest_args[2]
                except Exception as args_error:
                    logger.warning(f"Could not get args, using default voice: {args_error}")

            client = genai.Client(
                api_key=settings.gemini_api_key,
                http_options={"api_version": "v1alpha"}
            )
            
            config = {
                "response_modalities": ["AUDIO"],
                "speech_config": {
                    "voice_config": {
                        "prebuilt_voice_config": {
                            "voice_name": voice_name
                        }
                    }
                },
                "generation_config": {
                    "temperature": 0.8,
                    "max_output_tokens": 256,
                },
                "system_instruction": {
                    "parts": [{"text": SYSTEM_PROMPT}]
                }
            }
            
            async with client.aio.live.connect(
                model="gemini-2.0-flash-exp",
                config=config,
            ) as session:
                self.session = session
                self.session_id = f"video_{int(time.time())}"
                active_sessions[self.session_id] = {"type": "video", "handler": self}
                logger.info(f"Gemini Video Live session established with voice: {voice_name}")
                
                # Send identity reinforcement
                try:
                    identity_msg = (
                        f"IMPORTANT: You are Mai, the AI customer service assistant created by Critical Future. "
                        f"Your voice name is {voice_name}. You can see the person you're speaking with. "
                        f"Use their visual cues to provide better customer service and capture their contact information. "
                        f"Never say you are created by Google or Gemini. Always say Critical Future."
                    )
                    await session.send(input={"text": identity_msg})
                    
                    # Send uploaded files if any
                    if self.uploaded_files:
                        for file_data in self.uploaded_files:
                            await session.send(input=file_data)
                            logger.info("Uploaded file sent to AI")
                    
                    logger.info("Video session identity reinforcement sent")
                except Exception as prompt_error:
                    logger.warning(f"Could not send video session setup: {prompt_error}")
                
                while not self.quit.is_set():
                    turn = self.session.receive()
                    try:
                        async for response in turn:
                            if data := response.data:
                                audio = np.frombuffer(data, dtype=np.int16).reshape(1, -1)
                                self.audio_queue.put_nowait(audio)
                                
                            # Check for goodbye in text responses
                            if hasattr(response, 'text') and response.text:
                                if detect_goodbye(response.text):
                                    logger.info("Goodbye detected in video session, ending")
                                    await asyncio.sleep(2)  # Let the goodbye message play
                                    self.quit.set()
                                    break
                                    
                    except Exception as e:
                        logger.error(f"Video session error: {e}")
                        break
                        
        except Exception as e:
            logger.error(f"Error in GeminiVideoHandler start_up: {e}")
        finally:
            # Clean up session
            if self.session_id and self.session_id in active_sessions:
                del active_sessions[self.session_id]

    async def video_receive(self, frame: np.ndarray):
        """Receive video frame from client"""
        if self.session:
            # Send image every 2 seconds to avoid overwhelming the API
            if time.time() - self.last_frame_time > 2:
                self.last_frame_time = time.time()
                try:
                    await self.session.send(input=encode_image(frame))
                except Exception as e:
                    logger.warning(f"Error sending video frame: {e}")

    async def video_emit(self):
        """Video emit - AI doesn't send video back, only audio"""
        # Return None since AI doesn't send video back to user
        # FastRTC requires this method for send-receive mode
        return None

    async def receive(self, frame: tuple[int, np.ndarray]) -> None:
        """Receive audio from client"""
        try:
            _, array = frame
            array = array.squeeze()
            audio_message = encode_audio_dict(array)
            if self.session:
                await self.session.send(input=audio_message)
        except Exception as e:
            logger.error(f"Error processing audio in video session: {e}")

    async def emit(self):
        """Emit audio response to client"""
        try:
            array = await wait_for_item(self.audio_queue, 0.01)
            if array is not None:
                return (self.output_sample_rate, array)
            return None
        except Exception as e:
            logger.error(f"Error emitting audio in video session: {e}")
            return None

    async def shutdown(self) -> None:
        """Clean shutdown of video handler"""
        logger.info("Shutting down Gemini video handler")
        if self.session:
            self.quit.set()
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing video session: {e}")
            self.quit.clear()
            
        # Clean up session
        if self.session_id and self.session_id in active_sessions:
            del active_sessions[self.session_id]

# Enhanced Stream configuration with better error handling
if FASTRTC_AVAILABLE:
    try:
        # Audio-only stream
        audio_stream = Stream(
            modality="audio",
            mode="send-receive",
            handler=GeminiHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=5,
            time_limit=300,
        )
        
        # Video stream (user sends video, AI responds with audio - we just don't display AI video)
        video_stream = Stream(
            modality="audio-video", 
            mode="send-receive",  # Must use send-receive, we just won't display AI video
            handler=GeminiVideoHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
            concurrency_limit=3,
            time_limit=300,
        )
        
        logger.info("FastRTC Streams initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing FastRTC Streams: {e}")
        # Proper fallback with required arguments
        audio_stream = Stream(
            modality="audio",
            mode="send-receive", 
            handler=GeminiHandler(),
            rtc_configuration=get_cloudflare_turn_credentials_async,
        )
        video_stream = Stream(
            modality="audio-video",
            mode="send-receive",
            handler=GeminiVideoHandler(), 
            rtc_configuration=get_cloudflare_turn_credentials_async,
        )
else:
    # Fallback streams with proper arguments
    audio_stream = Stream(
        modality="audio",
        mode="send-receive",
        handler=GeminiHandler(),
    )
    video_stream = Stream(
        modality="audio-video", 
        mode="send-receive",
        handler=GeminiVideoHandler(),
    )
    logger.warning("Using fallback Stream implementations")

# Request/Response models
class ChatRequest(BaseModel):
    prompt: str
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 512

class ChatResponse(BaseModel):
    response: str

class InputData(BaseModel):
    webrtc_id: str
    voice_name: str
    mode: str = "audio"  # "audio" or "video"
    uploaded_files: Optional[List[dict]] = None

# Initialize FastAPI app
app = FastAPI(
    title="Mai - AI Voice Assistant for Critical Future",
    description="Mai is Critical Future's intelligent voice-to-voice AI customer service assistant with real-time voice and video capabilities",
    version="2.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for assets, CSS, and JS
# Mount assets directory
assets_dir = current_dir / "assets"
if assets_dir.exists():
    app.mount("/assets", StaticFiles(directory=str(assets_dir)), name="assets")
    logger.info("Assets mounted at /assets")

# Mount CSS file
css_file = current_dir / "styles.css"
if css_file.exists():
    from fastapi.responses import FileResponse

    @app.get("/styles.css")
    async def get_styles():
        return FileResponse(str(css_file), media_type="text/css")
    logger.info("CSS file mounted at /styles.css")

# Mount JS file
js_file = current_dir / "script.js"
if js_file.exists():
    @app.get("/script.js")
    async def get_script():
        return FileResponse(str(js_file), media_type="application/javascript")
    logger.info("JS file mounted at /script.js")

# Mount static directory if it exists
static_dir = current_dir / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info("Static files mounted")

# Mount FastRTC streams
try:
    audio_stream.mount(app, path="/audio")
    video_stream.mount(app, path="/video")
    if FASTRTC_AVAILABLE:
        logger.info("FastRTC streams mounted successfully")
    else:
        logger.info("Fallback streams mounted (WebRTC features limited)")
except Exception as e:
    logger.error(f"Error mounting streams: {e}")

@app.post("/upload_file")
async def upload_file(file: UploadFile = File(...)):
    """Upload file to share with Mai"""
    try:
        # Read file content
        file_content = await file.read()
        
        # Determine mime type
        mime_type = file.content_type or "application/octet-stream"
        
        # For images, convert to JPEG if needed
        if mime_type.startswith("image/"):
            try:
                pil_image = Image.open(BytesIO(file_content))
                with BytesIO() as output_bytes:
                    pil_image.save(output_bytes, "JPEG")
                    file_content = output_bytes.getvalue()
                mime_type = "image/jpeg"
            except Exception as img_error:
                logger.warning(f"Could not process image: {img_error}")
        
        # Encode file for Gemini
        encoded_file = encode_file_content(file_content, mime_type)
        
        logger.info(f"File uploaded: {file.filename}, type: {mime_type}, size: {len(file_content)} bytes")
        
        return {
            "status": "success",
            "filename": file.filename,
            "mime_type": mime_type,
            "size": len(file_content),
            "encoded_data": encoded_file
        }
        
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@app.post("/input_hook")
async def set_input_hook(body: InputData):
    """Set input parameters for WebRTC stream with validation"""
    try:
        if not FASTRTC_AVAILABLE:
            return {
                "status": "error",
                "message": "WebRTC features are not available in this deployment",
                "webrtc_id": body.webrtc_id,
                "voice": body.voice_name,
                "mode": body.mode
            }
        
        # Validate inputs
        if not body.webrtc_id or not body.voice_name:
            raise HTTPException(status_code=400, detail="Missing webrtc_id or voice_name")
        
        # Validate voice name
        valid_voices = ["Puck", "Charon", "Kore", "Fenrir", "Aoede"]
        if body.voice_name not in valid_voices:
            logger.warning(f"Invalid voice {body.voice_name}, using Aoede")
            body.voice_name = "Aoede"
        
        # Set input for appropriate stream
        if body.mode == "video":
            video_stream.set_input(body.webrtc_id, body.voice_name, body.uploaded_files)
        else:
            audio_stream.set_input(body.webrtc_id, body.voice_name)
            
        logger.info(f"Input set for WebRTC ID: {body.webrtc_id}, Voice: {body.voice_name}, Mode: {body.mode}")
        return {"status": "ok", "webrtc_id": body.webrtc_id, "voice": body.voice_name, "mode": body.mode}
        
    except Exception as e:
        logger.error(f"Error setting input: {e}")
        raise HTTPException(status_code=500, detail=f"Input hook failed: {str(e)}")

@app.post("/end_session")
async def end_session(session_id: str = None):
    """Manually end a session"""
    try:
        if session_id and session_id in active_sessions:
            session_info = active_sessions[session_id]
            handler = session_info["handler"]
            handler.shutdown()
            logger.info(f"Session {session_id} ended manually")
            return {"status": "success", "message": "Session ended"}
        else:
            # End all active sessions
            for sid, session_info in list(active_sessions.items()):
                handler = session_info["handler"]
                handler.shutdown()
            logger.info("All sessions ended")
            return {"status": "success", "message": "All sessions ended"}
    except Exception as e:
        logger.error(f"Error ending session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to end session: {str(e)}")

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Text-based chat endpoint using Gemini"""
    try:
        # Check for goodbye in text chat
        if detect_goodbye(request.prompt):
            return ChatResponse(response="Thank you for reaching out. I've made a note of everything, and one of our team will be in touch shortly by phone or email. Have a fantastic day!")
        
        # Initialize Gemini client for text
        if settings.use_vertex_ai and settings.google_cloud_project:
            client = genai.Client(
                vertexai=True,
                project=settings.google_cloud_project,
                location='us-central1'
            )
        else:
            client = genai.Client(
                api_key=settings.gemini_api_key,
            )

        # Generate response
        response = await client.aio.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=[
                {"parts": [{"text": f"System: {SYSTEM_PROMPT}\n\nUser: {request.prompt}"}]}
            ]
        )

        ai_response = response.text if hasattr(response, 'text') else str(response)
        
        logger.info(f"Text chat response generated: {len(ai_response)} characters")
        return ChatResponse(response=ai_response)

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        
        # Fallback response for Mai
        fallback_response = (
            "I'm experiencing a technical difficulty right now. "
            "Please try again, or feel free to use the contact form to reach our team directly."
        )
        return ChatResponse(response=fallback_response)

@app.websocket("/ws")
async def websocket_text_chat(websocket: WebSocket):
    """WebSocket endpoint for text-based chat with streaming responses"""
    await websocket.accept()
    logger.info("WebSocket connection established for text chat")
    
    try:
        while True:
            # Receive message from client
            user_msg = await websocket.receive_text()
            logger.info(f"Received WebSocket message: {user_msg[:100]}...")

            # Mai focuses on business inquiries, not crisis intervention

            # Check for goodbye
            if detect_goodbye(user_msg):
                goodbye_response = (
                    "Thank you for reaching out. I've made a note of everything, and one of our team "
                    "will be in touch shortly by phone or email. Have a fantastic day!"
                )
                await websocket.send_text(goodbye_response)
                continue

            try:
                # Initialize Gemini client
                if settings.use_vertex_ai and settings.google_cloud_project:
                    client = genai.Client(
                        vertexai=True,
                        project=settings.google_cloud_project,
                        location='us-central1'
                    )
                else:
                    client = genai.Client(
                        api_key=settings.gemini_api_key,
                    )

                # Generate response
                response = await client.aio.models.generate_content(
                    model="gemini-2.0-flash-exp",
                    contents=[
                        {"parts": [{"text": f"System: {SYSTEM_PROMPT}\n\nUser: {user_msg}"}]}
                    ]
                )

                # Send the complete response at once
                if hasattr(response, 'text') and response.text:
                    # Clean up the response text
                    clean_text = response.text
                    
                    # Remove common voice artifacts
                    artifacts_to_remove = [
                        "[pause]", "...", "… [pause] …", "[PAUSE]",
                        "*pause*", "(pause)", "... [pause] ...",
                        "[breath]", "*breath*", "(breath)"
                    ]
                    
                    for artifact in artifacts_to_remove:
                        clean_text = clean_text.replace(artifact, "")
                    
                    # Clean up extra spaces and line breaks
                    clean_text = " ".join(clean_text.split())
                    
                    await websocket.send_text(clean_text)
                else:
                    await websocket.send_text("I apologize, I couldn't generate a proper response. How else can I help you?")

            except Exception as e:
                logger.error(f"Error generating response: {e}")
                await websocket.send_text(
                    "I apologize, but I'm having technical difficulties. "
                    "Please try again or use the contact form to reach our team directly."
                )

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.send_text(f"Connection error occurred: {str(e)}")
        except:
            pass

@app.post("/api/contact")
async def submit_contact_form(form_data: ContactFormSubmission):
    """Handle contact form submission with email follow-up"""
    try:
        # Convert form data to contact info
        contact_info = form_data.to_contact_info()

        # Create a simple conversation memory for the form submission
        memory = ConversationMemory(
            session_id=f"contact_form_{int(time.time())}",
            session_type=SessionType.CONTACT_FORM
        )

        # Add form submission as a message
        memory.add_message(
            MessageType.USER,
            f"Contact form submission: {form_data.purpose}",
            metadata={
                "form_submission": True,
                "name": form_data.name,
                "email": str(form_data.email),
                "phone": form_data.phone,
                "company": form_data.company
            }
        )

        # Set the contact info in memory
        memory.contact_info = contact_info

        # Send follow-up emails
        email_result = await email_service.send_follow_up_emails(contact_info, memory)

        logger.info(f"Contact form processed: {form_data.name} ({form_data.email})")

        return {
            "status": "success",
            "message": "Thank you for contacting Critical Future. We'll be in touch shortly!",
            "email_status": email_result.status,
            "emails_sent": email_result.emails_sent
        }

    except Exception as e:
        logger.error(f"Error processing contact form: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process contact form: {str(e)}")

@app.get("/api/voices")
async def get_available_voices():
    """Get list of available Gemini voices"""
    return {
        "voices": [
            {"id": "Puck", "name": "Puck", "description": "Warm, conversational voice"},
            {"id": "Charon", "name": "Charon", "description": "Calm, reassuring voice"},
            {"id": "Kore", "name": "Kore", "description": "Gentle, empathetic voice"},
            {"id": "Fenrir", "name": "Fenrir", "description": "Strong, supportive voice"},
            {"id": "Aoede", "name": "Aoede", "description": "Melodic, soothing voice (recommended)"},
        ],
        "fastrtc_available": FASTRTC_AVAILABLE
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for Railway deployment"""
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "mai-voice-agent",
            "version": "2.1.0",
            "fastrtc_available": FASTRTC_AVAILABLE,
            "video_support": FASTRTC_AVAILABLE,
            "active_sessions": len(active_sessions)
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

@app.get("/debug")
async def debug_info():
    """Debug endpoint to check file existence and configuration"""
    html_file = current_dir / "index.html"
    static_dir = current_dir / "static"
    
    return {
        "current_directory": str(current_dir),
        "html_file_exists": html_file.exists(),
        "html_file_path": str(html_file),
        "static_dir_exists": static_dir.exists(),
        "gemini_api_key_set": bool(settings.gemini_api_key),
        "fastrtc_available": FASTRTC_AVAILABLE,
        "fastrtc_error": FASTRTC_ERROR,
        "video_support": FASTRTC_AVAILABLE,
        "active_sessions": list(active_sessions.keys()),
        "files_in_directory": [f.name for f in current_dir.iterdir() if f.is_file()][:10],
        "python_version": sys.version,
        "recommendations": [
            "Text chat is always available",
            "Voice and video chat require FastRTC compatibility",
            f"Current issue: {FASTRTC_ERROR}" if FASTRTC_ERROR else "No issues detected"
        ]
    }

@app.get("/", response_class=HTMLResponse)
async def get_main_page():
    """Serve the main HTML interface with video support"""
    try:
        # Check if index.html exists
        html_file = current_dir / "index.html"
        if html_file.exists():
            try:
                html_content = html_file.read_text(encoding='utf-8')
            except Exception as read_error:
                logger.error(f"Error reading index.html: {read_error}")
                return HTMLResponse(content=f"""
<!DOCTYPE html>
<html>
<head><title>Mai Voice Agent - File Error</title></head>
<body>
    <h1>File Read Error</h1>
    <p>Could not read index.html: {read_error}</p>
    <p><a href="/debug">Debug Info</a></p>
</body>
</html>
                """, status_code=503)
            
            # Get RTC configuration
            try:
                rtc_config = await get_cloudflare_turn_credentials_async()
            except Exception as rtc_error:
                logger.warning(f"Could not get RTC config, using fallback: {rtc_error}")
                rtc_config = {
                    "iceServers": [
                        {"urls": "stun:stun.l.google.com:19302"},
                        {"urls": "stun:stun1.l.google.com:19302"}
                    ]
                }
            
            # Replace configuration placeholder
            html_content = html_content.replace(
                "__RTC_CONFIGURATION__", 
                json.dumps(rtc_config)
            )
            
            return HTMLResponse(content=html_content)
        else:
            return HTMLResponse(content="""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Mai Voice Agent - Loading</title>
</head>
<body>
    <h1>� Mai Voice Agent</h1>
    <p>Loading Critical Future's AI assistant...</p>
    <p>Text chat is always available, voice and video features depend on FastRTC support.</p>
</body>
</html>
            """)
        
    except Exception as e:
        logger.error(f"Error serving main page: {e}")
        return HTMLResponse(
            content=f"""
<!DOCTYPE html>
<html>
<head><title>Mai Voice Agent - Error</title></head>
<body>
    <h1>Service Error</h1>
    <p>Error: {str(e)}</p>
    <p><a href="/health">Check Health</a> | <a href="/debug">Debug Info</a></p>
</body>
</html>
            """, 
            status_code=503
        )

if __name__ == "__main__":
    import uvicorn
    
    # Development vs Production configuration
    if os.getenv("ENVIRONMENT") == "development":
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=settings.port,
            reload=True,
            log_level="info"
        )
    else:
        # Production configuration
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=settings.port,
            workers=1,  # Single worker for WebRTC state management
            log_level="info"
        )