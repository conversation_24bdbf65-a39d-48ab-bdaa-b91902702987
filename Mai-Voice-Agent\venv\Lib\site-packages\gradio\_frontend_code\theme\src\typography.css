.prose {
	font-weight: var(--prose-text-weight);
	font-size: var(--text-md);
}

.prose * {
	color: var(--body-text-color);
}

.prose p {
	margin-bottom: var(--spacing-sm);
	line-height: var(--line-lg);
}

/* headings
–––––––––––––––––––––––––––––––––––––––––––––––––– */

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5 {
	margin: var(--spacing-xxl) 0 var(--spacing-lg);
	font-weight: var(--prose-header-text-weight);
	line-height: 1.3;
	color: var(--body-text-color);
}

.prose > *:first-child {
	margin-top: 0;
}

.prose h1 {
	font-size: var(--text-xxl);
}

.prose h2 {
	font-size: var(--text-xl);
}

.prose h3 {
	font-size: var(--text-lg);
}

.prose h4 {
	font-size: 1.1em;
}

.prose h5 {
	font-size: 1.05em;
}

/* lists
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.prose ul {
	list-style: circle inside;
}
.prose ol {
	list-style: decimal inside;
}

.prose ul > p,
.prose li > p {
	display: inline;
}
.prose ol,
.prose ul {
	margin-top: 0;
	padding-left: 0;
}
.prose ul ul,
.prose ul ol,
.prose ol ol,
.prose ol ul {
	margin: 0.5em 0 0.5em 0em;
	font-size: 90%;
	padding-inline-start: 2em;
}
.prose li {
	margin-bottom: 0.5em;
}

/* code
–––––––––––––––––––––––––––––––––––––––––––––––––– */

/* tables
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.prose th,
.prose td {
	border-bottom: 1px solid #e1e1e1;
	padding: var(--text-xs) var(--text-md);
	text-align: left;
}

/* spacing
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.prose button,
.prose .button {
	margin-bottom: var(--spacing-sm);
}
.prose input,
.prose textarea,
.prose select,
.prose fieldset {
	margin-bottom: var(--spacing-sm);
}
.prose pre,
.prose blockquote,
.prose dl,
.prose figure,
.prose table,
.prose p,
.prose ul,
.prose ol,
.prose form {
	margin-bottom: var(--spacing-md);
}

.prose table,
.prose tr,
.prose td,
.prose th {
	border: 1px solid var(--body-text-color);
}
.prose table {
	border-collapse: collapse;
	margin-bottom: var(--spacing-xxl);
}

/* links
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.prose a {
	color: var(--link-text-color);
	text-decoration: underline;
}

.prose a:visited {
	color: var(--link-text-color-visited);
}

.prose a:hover {
	color: var(--link-text-color-hover);
}
.prose a:active {
	color: var(--link-text-color-active);
}

/* misc
–––––––––––––––––––––––––––––––––––––––––––––––––– */

.prose hr {
	margin-top: 3em;
	margin-bottom: 3.5em;
	border-width: 0;
	border-top: 1px solid #e1e1e1;
}

.prose blockquote {
	margin: var(--size-6) 0 !important;
	border-left: 5px solid var(--border-color-primary);
	padding-left: var(--size-2);
}

.prose :last-child {
	margin-bottom: 0 !important;
}
