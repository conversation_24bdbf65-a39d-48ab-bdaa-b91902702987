"""
Configuration and settings for Mai Voice Agent
"""

import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, <PERSON>
from pydantic_settings import BaseSettings


class EmailSettings(BaseSettings):
    """Email configuration settings"""
    smtp_server: str = Field(default="smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    email_address: str = Field(..., env="EMAIL_ADDRESS")
    email_password: str = Field(..., env="EMAIL_PASSWORD")
    team_email: str = Field(default="", env="TEAM_EMAIL")  # If different from email_address
    
    class Config:
        env_file = ".env"
    
    @property
    def team_email_address(self) -> str:
        """Get the team email address (defaults to main email if not set)"""
        return self.team_email or self.email_address


class MaiSettings(BaseSettings):
    """Main settings for Mai Voice Agent"""
    # Gemini API settings
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    google_cloud_project: Optional[str] = Field(None, env="GOOGLE_CLOUD_PROJECT")
    use_vertex_ai: bool = Field(False, env="USE_VERTEX_AI")

    # Server settings
    port: int = Field(8000, env="PORT")
    environment: str = Field("production", env="ENVIRONMENT")
    host: str = Field("0.0.0.0", env="HOST")
    debug: bool = Field(False, env="DEBUG")

    # Email settings
    email_address: str = Field(..., env="EMAIL_ADDRESS")
    email_password: str = Field(..., env="EMAIL_PASSWORD")
    smtp_server: str = Field(default="smtp.gmail.com", env="SMTP_SERVER")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    team_email: str = Field(default="", env="TEAM_EMAIL")

    # Voice and session settings
    default_voice: str = Field("Aoede", env="DEFAULT_VOICE")
    session_timeout: int = Field(300, env="SESSION_TIMEOUT")
    max_concurrent_sessions: int = Field(10, env="MAX_CONCURRENT_SESSIONS")

    # Critical Future branding
    company_name: str = "Critical Future"
    company_website: str = "https://criticalfutureglobal.com"
    company_description: str = "AI & Strategy Development Agency"

    class Config:
        env_file = ".env"

    @property
    def team_email_address(self) -> str:
        """Get the team email address (defaults to main email if not set)"""
        return self.team_email or self.email_address


# Email templates
EMAIL_TEMPLATES = {
    "customer_confirmation": {
        "subject": "Thank you for contacting Critical Future",
        "template": """Dear {name},

Thank you for reaching out to Critical Future. We've received your inquiry and wanted to confirm that we have all the details.

**Your Contact Information:**
• Name: {name}
• Email: {email}
• Phone: {phone}
• Company: {company}
• Inquiry: {purpose}

**What Happens Next:**
One of our expert team members will review your inquiry and get back to you within 24 hours. We're excited to learn more about your AI and strategy needs.

**About Critical Future:**
We're a London-based AI and strategy powerhouse that has completed over 1,000 global consultancy projects across 50+ countries. Since 2014, we've been pioneering AI solutions - from deepfake generators to cancer prediction systems, recommendation engines, and AI-powered employees. We've helped generate over $1 billion in client opportunities.

**Conversation Summary:**
{conversation_summary}

Best regards,
The Critical Future Team

---
Critical Future | AI & Strategy Development Agency
Website: https://criticalfutureglobal.com
"""
    },
    
    "team_notification": {
        "subject": "New Lead: {name} from {company}",
        "template": """New lead captured by Mai Voice Agent:

**CONTACT DETAILS:**
• Name: {name}
• Email: {email}
• Phone: {phone}
• Company: {company}
• Purpose: {purpose}

**CONVERSATION SUMMARY:**
{conversation_summary}

**DETAILED CONVERSATION MEMORY:**
{conversation_memory}

**RECOMMENDED ACTIONS:**
• Review conversation for specific requirements
• Prepare relevant case studies or examples
• Schedule discovery call if appropriate
• Send relevant whitepapers or resources

This lead was captured via Mai Voice Agent and requires follow-up.

---
Mai Voice Agent | Critical Future
"""
    }
}


def get_email_template(template_name: str) -> Optional[Dict[str, str]]:
    """Get email template by name"""
    return EMAIL_TEMPLATES.get(template_name)


def get_critical_future_info() -> Dict[str, Any]:
    """Get Critical Future company information"""
    return {
        "name": "Critical Future",
        "description": "AI & Strategy Development Agency",
        "website": "https://criticalfutureglobal.com",
        "location": "London-based",
        "experience": "Since 2014",
        "projects": "1,000+ global consultancy projects",
        "countries": "50+ countries",
        "value_generated": "$1+ billion in client opportunities",
        "specialties": [
            "AI Development",
            "Strategy Consulting", 
            "Deepfake Technology",
            "Cancer Prediction Systems",
            "Recommendation Engines",
            "AI-Powered Employees",
            "Government Policy Advisory"
        ]
    }


# Load environment variables first
from dotenv import load_dotenv
current_dir = os.path.dirname(os.path.abspath(__file__))
env_path = os.path.join(current_dir, '.env')
load_dotenv(env_path)

# Initialize settings
try:
    settings = MaiSettings()
except Exception as e:
    # Fallback for development/testing
    import warnings
    warnings.warn(f"Could not load settings: {e}. Using defaults.")

    class FallbackSettings:
        gemini_api_key = os.getenv("GEMINI_API_KEY", "")
        google_cloud_project = os.getenv("GOOGLE_CLOUD_PROJECT")
        use_vertex_ai = False
        port = int(os.getenv("PORT", "8000"))
        environment = os.getenv("ENVIRONMENT", "development")
        email_address = os.getenv("EMAIL_ADDRESS", "")
        email_password = os.getenv("EMAIL_PASSWORD", "")
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        team_email = os.getenv("TEAM_EMAIL", "")
        host = os.getenv("HOST", "0.0.0.0")
        debug = os.getenv("DEBUG", "false").lower() == "true"
        default_voice = os.getenv("DEFAULT_VOICE", "Aoede")
        session_timeout = int(os.getenv("SESSION_TIMEOUT", "300"))
        max_concurrent_sessions = int(os.getenv("MAX_CONCURRENT_SESSIONS", "10"))
        company_name = "Critical Future"
        company_website = "https://criticalfutureglobal.com"
        company_description = "AI & Strategy Development Agency"

        @property
        def team_email_address(self):
            return self.team_email or self.email_address

    settings = FallbackSettings()


# Voice configuration
VOICE_CONFIG = {
    "default_voice": "Aoede",
    "available_voices": [
        {"id": "Puck", "name": "Puck", "description": "Warm, conversational voice"},
        {"id": "Charon", "name": "Charon", "description": "Calm, reassuring voice"},
        {"id": "Kore", "name": "Kore", "description": "Gentle, empathetic voice"},
        {"id": "Fenrir", "name": "Fenrir", "description": "Strong, supportive voice"},
        {"id": "Aoede", "name": "Aoede", "description": "Melodic, soothing voice (recommended)"},
    ],
    "generation_config": {
        "temperature": 0.8,
        "max_output_tokens": 256,
    }
}


# Session configuration
SESSION_CONFIG = {
    "max_duration_minutes": 30,
    "idle_timeout_minutes": 5,
    "max_concurrent_sessions": 10,
    "auto_save_interval_seconds": 30
}


# Contact extraction patterns
CONTACT_PATTERNS = {
    "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
    "phone": r'(\+?1?[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
    "name_indicators": ["my name is", "i'm", "i am", "this is"],
    "company_indicators": ["i work at", "i'm from", "company is", "organization is", "we are"]
}


# Topic keywords for conversation analysis
TOPIC_KEYWORDS = {
    "AI/Machine Learning": ["ai", "artificial intelligence", "machine learning", "ml", "deep learning", "neural network"],
    "Strategy/Consulting": ["strategy", "consulting", "business", "transformation", "planning"],
    "Technology": ["technology", "tech", "software", "development", "digital"],
    "Data/Analytics": ["data", "analytics", "analysis", "insights", "reporting"],
    "Automation": ["automation", "automate", "process", "workflow", "efficiency"]
}
