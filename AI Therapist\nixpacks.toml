[variables]
NIXPACKS_PYTHON_VERSION = "3.11"

[phases.setup]
nixPkgs = [
    "python311",
    "python311Packages.pip",
    "python311Packages.setuptools",
    "python311Packages.wheel",
    "pkg-config",
    "libffi",
    "openssl",
    "portaudio",
    "ffmpeg",
    "gstreamer",
    "gst-plugins-base",
    "gst-plugins-good"
]

[phases.install]
cmds = [
    "pip install --upgrade pip setuptools wheel",
    "pip install --no-cache-dir -r requirements.txt"
]

[phases.build]
cmds = ["echo 'Build phase complete'"]

[start]
cmd = "python main.py"
