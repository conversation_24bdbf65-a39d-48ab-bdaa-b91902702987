"""
Email service for Mai Voice Agent
Handles sending follow-up emails to customers and Critical Future team
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, Optional
from datetime import datetime

from config import settings, get_email_template
from models import ContactInfo, ConversationMemory, EmailResponse

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending emails with conversation follow-ups"""
    
    def __init__(self):
        self.smtp_server = settings.smtp_server
        self.smtp_port = settings.smtp_port
        self.email_address = settings.email_address
        self.email_password = settings.email_password
    
    async def send_email(self, to_email: str, subject: str, body: str, is_html: bool = False) -> bool:
        """Send email using configured SMTP settings"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_address
            msg['To'] = to_email
            msg['Subject'] = subject
            
            if is_html:
                msg.attach(MIMEText(body, 'html'))
            else:
                msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.email_address, self.email_password)
            text = msg.as_string()
            server.sendmail(self.email_address, to_email, text)
            server.quit()
            
            logger.info(f"✅ Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send email to {to_email}: {e}")
            return False
    
    def format_contact_info(self, contact_info: ContactInfo) -> Dict[str, str]:
        """Format contact info for email templates"""
        return {
            "name": contact_info.name or "Valued Contact",
            "company": contact_info.company or "Not specified",
            "email": contact_info.email or "Not provided",
            "phone": contact_info.phone or "Not provided",
            "purpose": contact_info.purpose or "General enquiry"
        }
    
    def generate_conversation_summary(self, memory: ConversationMemory) -> str:
        """Generate a formatted conversation summary"""
        if not memory.messages:
            return "No conversation recorded."
        
        summary_lines = []
        summary_lines.append(f"Session Type: {memory.session_type.value.title()}")
        summary_lines.append(f"Started: {memory.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        summary_lines.append(f"Duration: {(memory.last_activity - memory.started_at).total_seconds():.0f} seconds")
        summary_lines.append("")
        summary_lines.append("Conversation:")
        
        for msg in memory.messages[-10:]:  # Last 10 messages
            speaker = "User" if msg.type.value == "user" else "Mai"
            timestamp = msg.timestamp.strftime('%H:%M:%S')
            summary_lines.append(f"[{timestamp}] {speaker}: {msg.content}")
        
        return "\n".join(summary_lines)
    
    def generate_conversation_memory(self, memory: ConversationMemory) -> str:
        """Generate detailed conversation memory for team"""
        memory_lines = []
        memory_lines.append("DETAILED CONVERSATION MEMORY:")
        memory_lines.append("=" * 50)

        # Session info
        memory_lines.append(f"Session ID: {memory.session_id}")
        memory_lines.append(f"Type: {memory.session_type.value.title()}")
        memory_lines.append(f"Started: {memory.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        memory_lines.append(f"Last Activity: {memory.last_activity.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        duration = (memory.last_activity - memory.started_at).total_seconds()
        memory_lines.append(f"Duration: {duration:.0f} seconds ({duration/60:.1f} minutes)")
        memory_lines.append("")

        # Contact extraction
        extracted_info = memory.contact_info
        if any([extracted_info.name, extracted_info.email, extracted_info.phone, extracted_info.company]):
            memory_lines.append("EXTRACTED CONTACT INFO:")
            if extracted_info.name:
                memory_lines.append(f"- Name: {extracted_info.name}")
            if extracted_info.email:
                memory_lines.append(f"- Email: {extracted_info.email}")
            if extracted_info.phone:
                memory_lines.append(f"- Phone: {extracted_info.phone}")
            if extracted_info.company:
                memory_lines.append(f"- Company: {extracted_info.company}")
            if extracted_info.purpose:
                memory_lines.append(f"- Purpose: {extracted_info.purpose}")
            memory_lines.append("")

        # Conversation analysis
        user_messages = [msg for msg in memory.messages if msg.type.value == "user"]
        mai_messages = [msg for msg in memory.messages if msg.type.value == "mai"]

        memory_lines.append("CONVERSATION ANALYSIS:")
        memory_lines.append(f"- Total messages: {len(memory.messages)}")
        memory_lines.append(f"- User messages: {len(user_messages)}")
        memory_lines.append(f"- Mai responses: {len(mai_messages)}")
        memory_lines.append("")

        # Key topics mentioned (simple keyword extraction)
        all_user_text = " ".join([msg.content.lower() for msg in user_messages])
        key_topics = []
        topic_keywords = {
            "AI/Machine Learning": ["ai", "artificial intelligence", "machine learning", "ml", "deep learning", "neural network"],
            "Strategy/Consulting": ["strategy", "consulting", "business", "transformation", "planning"],
            "Technology": ["technology", "tech", "software", "development", "digital"],
            "Data/Analytics": ["data", "analytics", "analysis", "insights", "reporting"],
            "Automation": ["automation", "automate", "process", "workflow", "efficiency"]
        }

        for topic, keywords in topic_keywords.items():
            if any(keyword in all_user_text for keyword in keywords):
                key_topics.append(topic)

        if key_topics:
            memory_lines.append("KEY TOPICS MENTIONED:")
            for topic in key_topics:
                memory_lines.append(f"- {topic}")
            memory_lines.append("")

        # Full conversation
        memory_lines.append("FULL CONVERSATION:")
        memory_lines.append("-" * 30)

        for i, msg in enumerate(memory.messages, 1):
            speaker = "User" if msg.type.value == "user" else "Mai"
            timestamp = msg.timestamp.strftime('%H:%M:%S')
            memory_lines.append(f"{i:2d}. [{timestamp}] {speaker}: {msg.content}")

            # Add metadata if present
            if msg.metadata:
                for key, value in msg.metadata.items():
                    memory_lines.append(f"    Metadata - {key}: {value}")

        # Add follow-up recommendations
        memory_lines.append("")
        memory_lines.append("RECOMMENDED FOLLOW-UP ACTIONS:")
        memory_lines.append("- Review conversation for specific requirements")
        memory_lines.append("- Prepare relevant case studies or examples")
        memory_lines.append("- Schedule discovery call if appropriate")
        memory_lines.append("- Send relevant whitepapers or resources")

        return "\n".join(memory_lines)
    
    async def send_customer_confirmation(self, contact_info: ContactInfo, memory: ConversationMemory) -> bool:
        """Send confirmation email to customer"""
        if not contact_info.email:
            logger.warning("No customer email provided, skipping confirmation email")
            return False
        
        template = get_email_template("customer_confirmation")
        if not template:
            logger.error("Customer confirmation email template not found")
            return False
        
        # Format contact info
        contact_data = self.format_contact_info(contact_info)
        
        # Generate conversation summary
        conversation_summary = self.generate_conversation_summary(memory)
        
        # Format email
        subject = template["subject"]
        body = template["template"].format(
            **contact_data,
            conversation_summary=conversation_summary
        )
        
        return await self.send_email(contact_info.email, subject, body)
    
    async def send_team_notification(self, contact_info: ContactInfo, memory: ConversationMemory) -> bool:
        """Send notification email to Critical Future team"""
        template = get_email_template("team_notification")
        if not template:
            logger.error("Team notification email template not found")
            return False
        
        # Format contact info
        contact_data = self.format_contact_info(contact_info)
        
        # Generate conversation summary and memory
        conversation_summary = self.generate_conversation_summary(memory)
        conversation_memory = self.generate_conversation_memory(memory)
        
        # Format email
        subject = template["subject"].format(**contact_data)
        body = template["template"].format(
            **contact_data,
            conversation_summary=conversation_summary,
            conversation_memory=conversation_memory
        )
        
        return await self.send_email(settings.email_address, subject, body)
    
    async def send_follow_up_emails(self, contact_info: ContactInfo, memory: ConversationMemory) -> EmailResponse:
        """Send both customer confirmation and team notification emails"""
        emails_sent = 0
        messages = []
        
        try:
            # Send customer confirmation
            if contact_info.email:
                customer_success = await self.send_customer_confirmation(contact_info, memory)
                if customer_success:
                    emails_sent += 1
                    messages.append(f"Confirmation sent to {contact_info.email}")
                else:
                    messages.append(f"Failed to send confirmation to {contact_info.email}")
            else:
                messages.append("No customer email provided")
            
            # Send team notification
            team_success = await self.send_team_notification(contact_info, memory)
            if team_success:
                emails_sent += 1
                messages.append(f"Team notification sent to {settings.email_address}")
            else:
                messages.append(f"Failed to send team notification to {settings.email_address}")
            
            # Determine overall status
            if emails_sent > 0:
                status = "success"
                message = f"Successfully sent {emails_sent} email(s). " + "; ".join(messages)
            else:
                status = "error"
                message = "Failed to send any emails. " + "; ".join(messages)
            
            logger.info(f"Email follow-up completed: {emails_sent} emails sent")
            
            return EmailResponse(
                status=status,
                message=message,
                emails_sent=emails_sent
            )
            
        except Exception as e:
            logger.error(f"Error in send_follow_up_emails: {e}")
            return EmailResponse(
                status="error",
                message=f"Email service error: {str(e)}",
                emails_sent=emails_sent
            )
    
    async def send_simple_follow_up(self, contact_data: Dict[str, Any], conversation_text: str) -> EmailResponse:
        """Send follow-up emails with simple contact data (for backwards compatibility)"""
        # Convert simple contact data to ContactInfo model
        contact_info = ContactInfo(
            name=contact_data.get("name"),
            email=contact_data.get("email"),
            phone=contact_data.get("phone"),
            company=contact_data.get("company"),
            purpose=contact_data.get("purpose")
        )
        
        # Create a simple memory object
        from models import ConversationMemory, MessageType
        memory = ConversationMemory(session_id="simple_followup")
        
        # Add conversation text as a single message
        if conversation_text:
            memory.add_message(MessageType.SYSTEM, conversation_text)
        
        return await self.send_follow_up_emails(contact_info, memory)

# Global email service instance
email_service = EmailService()
